#!/usr/bin/env python3
"""
🔄 CONSTRUCTION SCHÉMA TRANSFORM
Construction du schéma transform avec types EXACTS copiés depuis staging
Applique les transformations de nettoyage et normalisation

⚠️ ATTENTION: Supprime et recrée le schéma transform complet
"""

import psycopg2
import logging
from datetime import datetime
import time

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'build_transform_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TransformSchemaBuilder:
    """
    Construction du schéma transform avec types EXACTS depuis staging
    """
    
    def __init__(self):
        # Configuration Data Warehouse
        self.dw_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # Configuration Jira (source pour validation)
        self.jira_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'jiradb',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # 52 tables critiques complètes
        self.all_critical_tables = [
            # 🔥 CORE_BUSINESS (12 tables)
            'jiraissue', 'project', 'component', 'projectversion',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelink', 'issuelinktype', 'label', 'nodeassociation',
            
            # 👥 USERS_GROUPS (8 tables)
            'cwd_user', 'cwd_group', 'cwd_membership', 'cwd_user_attributes',
            'userassociation', 'projectroleactor', 'app_user', 'cwd_directory',
            
            # 🔄 WORKFLOWS (5 tables)
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',
            'os_currentstep', 'os_wfentry',
            
            # ⚙️ CONFIGURATION (6 tables)
            'fieldconfiguration', 'fieldconfigscheme', 'permissionscheme',
            'schemepermissions', 'fieldscreen', 'fieldscreentab',
            
            # 📝 CHANGES_HISTORY (3 tables)
            'changegroup', 'changeitem', 'jiraaction',
            
            # 🔧 PLUGINS_MANAGEMENT (2 tables)
            'pluginversion', 'managedconfigurationitem',
            
            # 📊 LOOKUP_TABLES (6 tables)
            'issuestatus', 'priority', 'resolution', 'issuetype',
            'projectrole', 'projectcategory',
            
            # 🤖 SCRIPT_RUNNER (4 tables)
            'AO_4B00E6_SR_USER_PROP', 'AO_4B00E6_STASH_SETTINGS',
            'AO_4B00E6_UPGRADE_BACKUP', 'AO_786AC3_SQL_FAVOURITE',
            
            # 🎯 AGILE_BOARDS (3 tables)
            'AO_60DB71_RAPIDVIEW', 'AO_60DB71_SPRINT', 'AO_60DB71_ISSUERANKING',
            
            # 🔍 JSM_AUDIT (3 tables)
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]
        
        # Colonnes à supprimer par table (DROP CANDIDATES)
        self.columns_to_drop = {
            'jiraissue': ['archived', 'moved', 'thumbnail'],
            'project': ['url'],  # Server-specific URLs
            'cwd_user': ['lower_user_name', 'lower_email_address'],
            'cwd_group': ['lower_group_name'],
            # Ajouter d'autres tables selon l'analyse
        }
        
        # Colonnes utilisateur à normaliser
        self.user_columns_to_normalize = {
            'jiraissue': ['reporter', 'assignee', 'creator'],
            'project': ['lead'],
            'worklog': ['author', 'updateauthor'],
            'changegroup': ['author'],
            'component': ['lead'],
            # Ajouter d'autres selon les besoins
        }
        
        self.stats = {
            'start_time': datetime.now(),
            'tables_created': 0,
            'columns_dropped': 0,
            'columns_normalized': 0,
            'total_columns': 0,
            'errors': []
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = False
            return conn
        except Exception as e:
            logger.error(f"❌ Erreur connexion DW: {e}")
            raise
    
    def connect_jira(self):
        """Connexion READ-ONLY à Jira pour validation"""
        try:
            conn = psycopg2.connect(**self.jira_config)
            conn.set_session(readonly=True, autocommit=True)
            return conn
        except Exception as e:
            logger.error(f"❌ Erreur connexion Jira: {e}")
            raise
    
    def create_transform_schema(self):
        """Créer le schéma transform"""
        logger.info("🏗️ CRÉATION SCHÉMA TRANSFORM")
        logger.info("=" * 60)
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Supprimer le schéma existant
            cursor.execute('DROP SCHEMA IF EXISTS transform CASCADE')
            logger.info("🗑️ Ancien schéma transform supprimé")
            
            # Créer le nouveau schéma
            cursor.execute('CREATE SCHEMA transform')
            logger.info("✅ Nouveau schéma transform créé")
            
            dw_conn.commit()
            
        except Exception as e:
            logger.error(f"❌ Erreur création schéma: {e}")
            dw_conn.rollback()
            raise
        finally:
            dw_conn.close()
    
    def get_staging_table_structure(self, table_name):
        """Récupérer la structure EXACTE d'une table staging"""
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            cursor.execute("""
                SELECT 
                    column_name,
                    data_type,
                    character_maximum_length,
                    numeric_precision,
                    numeric_scale,
                    is_nullable,
                    ordinal_position
                FROM information_schema.columns
                WHERE table_schema = 'staging' AND table_name = %s
                AND column_name NOT IN ('instance_id', 'extracted_at')
                ORDER BY ordinal_position
            """, (table_name,))
            
            columns = cursor.fetchall()
            
            if not columns:
                logger.warning(f"⚠️ Table staging.{table_name} non trouvée")
                return None
            
            return columns
            
        except Exception as e:
            logger.error(f"❌ Erreur récupération structure {table_name}: {e}")
            return None
        finally:
            dw_conn.close()
    
    def build_transform_column_definitions(self, table_name, staging_columns):
        """Construire les définitions de colonnes pour transform avec modifications"""
        column_definitions = []
        columns_dropped = 0
        columns_normalized = 0
        
        # Colonnes à supprimer pour cette table
        drop_columns = self.columns_to_drop.get(table_name, [])
        
        # Colonnes utilisateur à normaliser pour cette table
        user_columns = self.user_columns_to_normalize.get(table_name, [])
        
        for col_name, data_type, char_max_len, num_precision, num_scale, is_nullable, position in staging_columns:
            
            # SKIP: Colonnes à supprimer
            if col_name in drop_columns:
                logger.info(f"   🗑️ DROP: {col_name}")
                columns_dropped += 1
                continue
            
            # Construire le type PostgreSQL EXACT (copié de staging)
            if data_type == 'character varying':
                if char_max_len:
                    pg_type = f'VARCHAR({char_max_len})'
                else:
                    pg_type = 'TEXT'
            elif data_type == 'character':
                if char_max_len:
                    pg_type = f'CHAR({char_max_len})'
                else:
                    pg_type = 'CHAR(1)'
            elif data_type == 'text':
                pg_type = 'TEXT'
            elif data_type == 'numeric':
                if num_precision and num_scale is not None:
                    pg_type = f'NUMERIC({num_precision},{num_scale})'
                elif num_precision:
                    pg_type = f'NUMERIC({num_precision})'
                else:
                    pg_type = 'NUMERIC'
            elif data_type == 'integer':
                pg_type = 'INTEGER'
            elif data_type == 'bigint':
                pg_type = 'BIGINT'
            elif data_type == 'smallint':
                pg_type = 'SMALLINT'
            elif data_type == 'double precision':
                pg_type = 'DOUBLE PRECISION'
            elif data_type == 'real':
                pg_type = 'REAL'
            elif data_type == 'timestamp with time zone':
                pg_type = 'TIMESTAMPTZ'
            elif data_type == 'timestamp without time zone':
                pg_type = 'TIMESTAMP'
            elif data_type == 'date':
                pg_type = 'DATE'
            elif data_type == 'boolean':
                pg_type = 'BOOLEAN'
            elif data_type == 'bytea':
                pg_type = 'BYTEA'
            else:
                pg_type = 'TEXT'
                logger.warning(f"⚠️ Type non reconnu '{data_type}' pour {table_name}.{col_name}")
            
            # Contrainte NULL/NOT NULL
            null_constraint = '' if is_nullable == 'YES' else ' NOT NULL'
            
            # KEEP: Colonne originale
            column_def = f'    "{col_name}" {pg_type}{null_constraint}'
            column_definitions.append(column_def)
            
            # ADD: Colonnes normalisées pour les utilisateurs
            if col_name in user_columns:
                # Ajouter les colonnes dénormalisées
                column_definitions.extend([
                    f'    "{col_name}_username" VARCHAR(255)',
                    f'    "{col_name}_email" VARCHAR(255)',
                    f'    "{col_name}_display_name" VARCHAR(255)'
                ])
                columns_normalized += 1
                logger.info(f"   🔄 NORMALIZE: {col_name} → +3 user columns")
        
        self.stats['columns_dropped'] += columns_dropped
        self.stats['columns_normalized'] += columns_normalized
        
        return column_definitions

    def create_transform_table(self, table_name, column_definitions):
        """Créer une table transform avec types EXACTS + colonnes transform"""
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()

        try:
            # Ajouter les colonnes transform standard
            transform_columns = [
                '    instance_id INTEGER NOT NULL',
                '    transformed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                '    data_quality_score INTEGER DEFAULT 100',
                '    has_null_data BOOLEAN DEFAULT FALSE'
            ]

            all_columns = column_definitions + transform_columns
            columns_ddl = ',\n'.join(all_columns)

            # Nom de table avec suffix _clean
            transform_table_name = f"{table_name}_clean"

            # Créer la table
            create_sql = f'''
                CREATE TABLE transform."{transform_table_name}" (
                {columns_ddl}
                )
            '''

            cursor.execute(f'DROP TABLE IF EXISTS transform."{transform_table_name}" CASCADE')
            cursor.execute(create_sql)
            dw_conn.commit()

            self.stats['tables_created'] += 1
            self.stats['total_columns'] += len(column_definitions)

            logger.info(f"   ✅ transform.{transform_table_name}: {len(column_definitions)} colonnes + 4 transform")

        except Exception as e:
            logger.error(f"   ❌ Erreur création transform.{table_name}_clean: {e}")
            dw_conn.rollback()
            self.stats['errors'].append(f"{table_name}: {e}")
            raise
        finally:
            dw_conn.close()

    def build_all_transform_tables(self):
        """Construire toutes les tables transform"""
        logger.info(f"🔄 CONSTRUCTION {len(self.all_critical_tables)} TABLES TRANSFORM")
        logger.info("=" * 70)

        for i, table_name in enumerate(self.all_critical_tables, 1):
            logger.info(f"🔧 [{i:2d}/{len(self.all_critical_tables)}] Construction transform.{table_name}_clean")

            try:
                # Récupérer la structure staging
                staging_columns = self.get_staging_table_structure(table_name)

                if staging_columns:
                    # Construire les définitions transform
                    column_definitions = self.build_transform_column_definitions(table_name, staging_columns)

                    # Créer la table transform
                    self.create_transform_table(table_name, column_definitions)

                    logger.info(f"   📊 {table_name}: {len(staging_columns)} staging → {len(column_definitions)} transform")
                else:
                    logger.error(f"   ❌ Impossible de récupérer la structure staging pour {table_name}")
                    self.stats['errors'].append(f"{table_name}: Structure staging non trouvée")

            except Exception as e:
                logger.warning(f"   ⚠️ {table_name}: Erreur construction - {str(e)[:50]}")
                self.stats['errors'].append(f"{table_name}: {e}")
                continue

    def verify_transform_schema(self):
        """Vérifier le schéma transform créé"""
        logger.info("\n🔍 VÉRIFICATION SCHÉMA TRANSFORM")
        logger.info("=" * 60)

        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()

        try:
            # Lister toutes les tables transform
            cursor.execute("""
                SELECT table_name,
                       (SELECT COUNT(*) FROM information_schema.columns
                        WHERE table_schema = 'transform' AND table_name = t.table_name) as column_count
                FROM information_schema.tables t
                WHERE table_schema = 'transform'
                ORDER BY table_name
            """)

            transform_tables = cursor.fetchall()

            logger.info(f"📊 Tables transform créées: {len(transform_tables)}")

            for table_name, column_count in transform_tables:
                original_name = table_name.replace('_clean', '')
                logger.info(f"   ✅ transform.{table_name}: {column_count} colonnes")

            return len(transform_tables)

        finally:
            dw_conn.close()

def main():
    """Point d'entrée principal"""
    print("🔄 CONSTRUCTION SCHÉMA TRANSFORM")
    print("=" * 60)
    print("📋 Copie des types EXACTS depuis staging")
    print("🔧 Application des transformations de nettoyage")
    print("🎯 Schéma: transform.*_clean")
    print("=" * 60)

    builder = TransformSchemaBuilder()

    try:
        # Créer le schéma transform
        builder.create_transform_schema()

        # Construire toutes les tables
        builder.build_all_transform_tables()

        # Vérifier le schéma créé
        total_tables = builder.verify_transform_schema()

        # Statistiques finales
        duration = (datetime.now() - builder.stats['start_time']).total_seconds()

        logger.info(f"\n🎉 CONSTRUCTION TRANSFORM TERMINÉE!")
        logger.info(f"⏱️ Durée: {duration:.2f} secondes")
        logger.info(f"📊 Tables créées: {builder.stats['tables_created']}")
        logger.info(f"🗑️ Colonnes supprimées: {builder.stats['columns_dropped']}")
        logger.info(f"🔄 Colonnes normalisées: {builder.stats['columns_normalized']}")
        logger.info(f"📈 Total colonnes: {builder.stats['total_columns']}")
        logger.info(f"❌ Erreurs: {len(builder.stats['errors'])}")

        if builder.stats['errors']:
            logger.warning(f"🚨 Erreurs rencontrées:")
            for error in builder.stats['errors'][:5]:
                logger.warning(f"   - {error}")

        if total_tables >= 50:  # Au moins 50/52 tables
            logger.info(f"\n🎯 SCHÉMA TRANSFORM RÉUSSI!")
            logger.info(f"   {total_tables}/52 tables transform disponibles")
            logger.info(f"\n🎯 PROCHAINE ÉTAPE:")
            logger.info(f"   Exécuter: python transform_staging_data.py")
            logger.info(f"   Pour transformer les données staging → transform")
        else:
            logger.warning(f"\n⚠️ SCHÉMA TRANSFORM PARTIEL")
            logger.warning(f"   Seulement {total_tables}/52 tables disponibles")

    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
