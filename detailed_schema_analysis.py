#!/usr/bin/env python3
"""
🔍 DETAILED SCHEMA ANALYSIS
Deep dive into fact table structure and potential FK relationships

FOCUS AREAS:
1. Fact table column analysis
2. Potential FK relationships based on column names
3. Data type consistency issues
4. Star schema optimization opportunities
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_to_database(dbname):
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database=dbname,
            user="jirauser",
            password="mypassword"
        )
        return conn
    except Exception as e:
        logger.error(f"Error connecting to {dbname}: {e}")
        return None

def analyze_fact_table_structure():
    """Analyze fact_issues table structure in detail"""
    logger.info("🔍 DETAILED FACT TABLE ANALYSIS")
    logger.info("="*60)
    
    conn = connect_to_database("aya")
    if not conn:
        return
    
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    
    # Get fact_issues structure
    cursor.execute("""
        SELECT 
            column_name,
            data_type,
            character_maximum_length,
            numeric_precision,
            numeric_scale,
            is_nullable
        FROM information_schema.columns 
        WHERE table_schema = 'load' AND table_name = 'fact_issues'
        ORDER BY ordinal_position
    """)
    
    fact_columns = cursor.fetchall()
    
    logger.info(f"📊 FACT_ISSUES TABLE STRUCTURE ({len(fact_columns)} columns):")
    logger.info("-" * 60)
    
    potential_fks = []
    
    for col in fact_columns:
        col_name = col['column_name']
        data_type = col['data_type']
        
        # Check for potential FK columns
        is_potential_fk = False
        suggested_dim_table = None
        
        if col_name.endswith('_id') and col_name != 'id':
            base_name = col_name[:-3]
            suggested_dim_table = f"dim_{base_name}"
            is_potential_fk = True
        elif col_name.endswith('_key'):
            base_name = col_name[:-4]
            suggested_dim_table = f"dim_{base_name}"
            is_potential_fk = True
        elif col_name in ['reporter', 'assignee', 'creator']:
            suggested_dim_table = "dim_users"
            is_potential_fk = True
        elif col_name in ['project']:
            suggested_dim_table = "dim_projects"
            is_potential_fk = True
        elif col_name in ['issuetype']:
            suggested_dim_table = "dim_issue_types"
            is_potential_fk = True
        elif col_name in ['priority']:
            suggested_dim_table = "dim_priorities"
            is_potential_fk = True
        elif col_name in ['status']:
            suggested_dim_table = "dim_statuses"
            is_potential_fk = True
        
        status_icon = "🔗" if is_potential_fk else "📊"
        fk_info = f" → {suggested_dim_table}" if suggested_dim_table else ""
        
        logger.info(f"  {status_icon} {col_name:<25} {data_type:<15} {fk_info}")
        
        if is_potential_fk:
            potential_fks.append({
                'column': col_name,
                'suggested_table': suggested_dim_table
            })
    
    cursor.close()
    conn.close()
    
    logger.info(f"\n📊 POTENTIAL FOREIGN KEYS: {len(potential_fks)}")
    return potential_fks

def check_dimension_table_existence():
    """Check which dimension tables actually exist"""
    logger.info("\n🏗️ CHECKING DIMENSION TABLE EXISTENCE")
    logger.info("="*60)
    
    conn = connect_to_database("aya")
    if not conn:
        return
    
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    
    # Get all dimension tables
    cursor.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'load' 
        AND table_name LIKE 'dim_%'
        ORDER BY table_name
    """)
    
    existing_dims = [row['table_name'] for row in cursor.fetchall()]
    
    logger.info(f"📊 EXISTING DIMENSION TABLES ({len(existing_dims)}):")
    for dim_table in existing_dims:
        # Get record count
        try:
            cursor.execute(f'SELECT COUNT(*) FROM load."{dim_table}"')
            result = cursor.fetchone()
            count = result['count'] if result else 0
        except Exception as e:
            count = 0
        logger.info(f"  ✅ {dim_table:<30} ({count:,} records)")
    
    cursor.close()
    conn.close()
    
    return existing_dims

def analyze_data_type_issues():
    """Look for specific data type issues"""
    logger.info("\n🔧 ANALYZING DATA TYPE ISSUES")
    logger.info("="*60)
    
    conn = connect_to_database("aya")
    if not conn:
        return
    
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    
    # Check for common data type issues
    issues = []
    
    # 1. Check for VARCHAR columns that should be NUMERIC
    cursor.execute("""
        SELECT table_name, column_name, data_type
        FROM information_schema.columns 
        WHERE table_schema = 'load'
        AND data_type = 'character varying'
        AND (column_name LIKE '%_id' OR column_name LIKE '%id')
        ORDER BY table_name, column_name
    """)
    
    varchar_ids = cursor.fetchall()
    
    if varchar_ids:
        logger.info("⚠️ VARCHAR columns that might should be NUMERIC:")
        for row in varchar_ids:
            logger.info(f"  {row['table_name']}.{row['column_name']} ({row['data_type']})")
            issues.append({
                'type': 'varchar_id',
                'table': row['table_name'],
                'column': row['column_name'],
                'current_type': row['data_type'],
                'suggested_type': 'NUMERIC'
            })
    
    # 2. Check for inconsistent timestamp formats
    cursor.execute("""
        SELECT table_name, column_name, data_type
        FROM information_schema.columns 
        WHERE table_schema = 'load'
        AND column_name LIKE '%date%' OR column_name LIKE '%time%' OR column_name LIKE '%created%' OR column_name LIKE '%updated%'
        ORDER BY table_name, column_name
    """)
    
    date_columns = cursor.fetchall()
    
    if date_columns:
        logger.info("\n📅 DATE/TIME columns:")
        for row in date_columns:
            logger.info(f"  {row['table_name']}.{row['column_name']} ({row['data_type']})")
    
    # 3. Check for TEXT columns that might be too large
    cursor.execute("""
        SELECT table_name, column_name, data_type, character_maximum_length
        FROM information_schema.columns 
        WHERE table_schema = 'load'
        AND data_type = 'text'
        ORDER BY table_name, column_name
    """)
    
    text_columns = cursor.fetchall()
    
    if text_columns:
        logger.info("\n📝 TEXT columns (might need VARCHAR limits):")
        for row in text_columns:
            logger.info(f"  {row['table_name']}.{row['column_name']} ({row['data_type']})")
            issues.append({
                'type': 'text_column',
                'table': row['table_name'],
                'column': row['column_name'],
                'current_type': row['data_type'],
                'suggested_type': 'VARCHAR(500)'
            })
    
    cursor.close()
    conn.close()
    
    logger.info(f"\n📊 DATA TYPE ISSUES FOUND: {len(issues)}")
    return issues

def suggest_foreign_key_relationships():
    """Suggest specific foreign key relationships"""
    logger.info("\n🔗 SUGGESTING FOREIGN KEY RELATIONSHIPS")
    logger.info("="*60)
    
    conn = connect_to_database("aya")
    if not conn:
        return
    
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    
    # Get fact table columns
    cursor.execute("""
        SELECT column_name, data_type
        FROM information_schema.columns 
        WHERE table_schema = 'load' AND table_name = 'fact_issues'
        ORDER BY ordinal_position
    """)
    
    fact_columns = cursor.fetchall()
    
    # Get existing dimension tables
    cursor.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'load' 
        AND table_name LIKE 'dim_%'
    """)
    
    dim_tables = [row['table_name'] for row in cursor.fetchall()]
    
    suggested_fks = []
    
    for col in fact_columns:
        col_name = col['column_name']
        
        # Skip if it's already a measure or the primary key
        if col_name in ['id', 'instance_id'] or col_name.startswith('measure_'):
            continue
        
        # Suggest FK relationships
        suggested_dim = None
        
        if col_name == 'reporter':
            suggested_dim = 'dim_users'
        elif col_name == 'assignee':
            suggested_dim = 'dim_users'
        elif col_name == 'creator':
            suggested_dim = 'dim_users'
        elif col_name == 'project':
            suggested_dim = 'dim_projects'
        elif col_name == 'issuetype':
            suggested_dim = 'dim_issue_types'
        elif col_name == 'priority':
            suggested_dim = 'dim_priorities'
        elif col_name == 'status':
            suggested_dim = 'dim_statuses'
        elif col_name == 'resolution':
            suggested_dim = 'dim_resolutions'
        elif col_name.endswith('_id'):
            base_name = col_name[:-3]
            suggested_dim = f'dim_{base_name}'
        
        if suggested_dim and suggested_dim in dim_tables:
            suggested_fks.append({
                'fact_table': 'fact_issues',
                'fact_column': col_name,
                'dim_table': suggested_dim,
                'dim_column': 'id'
            })
            logger.info(f"  🔗 {col_name} → {suggested_dim}.id")
        elif suggested_dim:
            logger.warning(f"  ⚠️ {col_name} → {suggested_dim} (table doesn't exist)")
    
    cursor.close()
    conn.close()
    
    logger.info(f"\n📊 SUGGESTED FOREIGN KEYS: {len(suggested_fks)}")
    return suggested_fks

def main():
    """Main detailed analysis function"""
    logger.info("🔍 DETAILED SCHEMA ANALYSIS")
    logger.info("="*70)
    logger.info("Deep dive into fact table and FK relationships...")
    logger.info("="*70)
    
    # 1. Analyze fact table structure
    potential_fks = analyze_fact_table_structure()
    
    # 2. Check dimension table existence
    existing_dims = check_dimension_table_existence()
    
    # 3. Analyze data type issues
    type_issues = analyze_data_type_issues()
    
    # 4. Suggest FK relationships
    suggested_fks = suggest_foreign_key_relationships()
    
    # 5. Summary and recommendations
    logger.info("\n" + "="*70)
    logger.info("📊 DETAILED ANALYSIS SUMMARY")
    logger.info("="*70)
    logger.info(f"🏗️ Dimension tables: {len(existing_dims)}")
    logger.info(f"🔗 Potential FKs: {len(potential_fks)}")
    logger.info(f"🔧 Data type issues: {len(type_issues)}")
    logger.info(f"⭐ Suggested FKs: {len(suggested_fks)}")
    
    if len(type_issues) > 0 or len(suggested_fks) > 0:
        logger.info("\n🎯 RECOMMENDED ACTIONS:")
        if len(type_issues) > 0:
            logger.info("1. ✅ Create data type correction script")
        if len(suggested_fks) > 0:
            logger.info("2. ✅ Create foreign key creation script")
        logger.info("3. ✅ Add scripts to PRODUCTION_ETL_DEPLOYMENT")
    
    return {
        'existing_dims': existing_dims,
        'potential_fks': potential_fks,
        'type_issues': type_issues,
        'suggested_fks': suggested_fks
    }

if __name__ == "__main__":
    results = main()
