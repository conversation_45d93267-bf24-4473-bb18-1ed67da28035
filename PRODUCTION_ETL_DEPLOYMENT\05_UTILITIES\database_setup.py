#!/usr/bin/env python3
"""
🗄️ DATABASE SETUP UTILITY
Prepare target database for Jira Analytics ETL deployment

FEATURES:
- Create target database if it doesn't exist
- Setup required extensions
- Create schemas
- Set permissions
- Validate setup
"""

import psycopg2
import logging
import sys
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseSetup:
    """Setup and prepare database for ETL deployment"""
    
    def __init__(self, host="localhost", port=5432, admin_user="postgres", admin_password="postgres"):
        self.host = host
        self.port = port
        self.admin_user = admin_user
        self.admin_password = admin_password
    
    def get_admin_connection(self, database="postgres"):
        """Get admin connection to PostgreSQL"""
        try:
            conn = psycopg2.connect(
                host=self.host,
                port=self.port,
                database=database,
                user=self.admin_user,
                password=self.admin_password
            )
            conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            return conn
        except Exception as e:
            logger.error(f"❌ Failed to connect as admin: {e}")
            return None
    
    def database_exists(self, database_name):
        """Check if database exists"""
        conn = self.get_admin_connection()
        if not conn:
            return False
        
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (database_name,))
            exists = cursor.fetchone() is not None
            cursor.close()
            conn.close()
            return exists
        except Exception as e:
            logger.error(f"❌ Error checking database existence: {e}")
            conn.close()
            return False
    
    def create_database(self, database_name, owner="jirauser"):
        """Create new database"""
        if self.database_exists(database_name):
            logger.info(f"✅ Database '{database_name}' already exists")
            return True
        
        conn = self.get_admin_connection()
        if not conn:
            return False
        
        try:
            cursor = conn.cursor()
            
            # Create database
            cursor.execute(f'CREATE DATABASE "{database_name}" OWNER "{owner}"')
            logger.info(f"✅ Created database: {database_name}")
            
            cursor.close()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create database: {e}")
            conn.close()
            return False
    
    def create_user(self, username, password):
        """Create database user if it doesn't exist"""
        conn = self.get_admin_connection()
        if not conn:
            return False
        
        try:
            cursor = conn.cursor()
            
            # Check if user exists
            cursor.execute("SELECT 1 FROM pg_user WHERE usename = %s", (username,))
            if cursor.fetchone():
                logger.info(f"✅ User '{username}' already exists")
                cursor.close()
                conn.close()
                return True
            
            # Create user
            cursor.execute(f"CREATE USER \"{username}\" WITH PASSWORD '{password}'")
            cursor.execute(f"ALTER USER \"{username}\" CREATEDB")
            logger.info(f"✅ Created user: {username}")
            
            cursor.close()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create user: {e}")
            conn.close()
            return False
    
    def setup_database_extensions(self, database_name):
        """Setup required extensions in database"""
        conn = self.get_admin_connection(database_name)
        if not conn:
            return False
        
        try:
            cursor = conn.cursor()
            
            # Extensions that might be useful for analytics
            extensions = [
                "pg_stat_statements",  # Query performance monitoring
                "btree_gin",          # Additional index types
                "btree_gist"          # Additional index types
            ]
            
            for ext in extensions:
                try:
                    cursor.execute(f"CREATE EXTENSION IF NOT EXISTS {ext}")
                    logger.info(f"✅ Extension enabled: {ext}")
                except Exception as e:
                    logger.warning(f"⚠️ Could not enable {ext}: {e}")
            
            cursor.close()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup extensions: {e}")
            conn.close()
            return False
    
    def create_schemas(self, database_name, owner="jirauser"):
        """Create ETL schemas"""
        conn = self.get_admin_connection(database_name)
        if not conn:
            return False
        
        try:
            cursor = conn.cursor()
            
            schemas = ["staging", "transform", "load"]
            
            for schema in schemas:
                cursor.execute(f'CREATE SCHEMA IF NOT EXISTS "{schema}" AUTHORIZATION "{owner}"')
                logger.info(f"✅ Created schema: {schema}")
            
            # Grant permissions
            cursor.execute(f'GRANT ALL ON SCHEMA public TO "{owner}"')
            for schema in schemas:
                cursor.execute(f'GRANT ALL ON SCHEMA "{schema}" TO "{owner}"')
            
            logger.info(f"✅ Granted permissions to: {owner}")
            
            cursor.close()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create schemas: {e}")
            conn.close()
            return False
    
    def validate_setup(self, database_name, username):
        """Validate database setup"""
        logger.info("🔍 VALIDATING DATABASE SETUP")
        logger.info("="*40)
        
        # Test connection as target user
        try:
            conn = psycopg2.connect(
                host=self.host,
                port=self.port,
                database=database_name,
                user=username,
                password="mypassword"  # Default password
            )
            
            cursor = conn.cursor()
            
            # Check schemas
            cursor.execute("""
                SELECT schema_name FROM information_schema.schemata 
                WHERE schema_name IN ('public', 'staging', 'transform', 'load')
                ORDER BY schema_name
            """)
            
            schemas = [row[0] for row in cursor.fetchall()]
            expected_schemas = ['load', 'public', 'staging', 'transform']
            
            logger.info(f"Schemas found: {schemas}")
            
            if set(schemas) >= set(expected_schemas):
                logger.info("✅ All required schemas present")
                validation_ok = True
            else:
                logger.error("❌ Missing required schemas")
                validation_ok = False
            
            # Test table creation
            try:
                cursor.execute("CREATE TABLE IF NOT EXISTS public.test_table (id INTEGER)")
                cursor.execute("DROP TABLE public.test_table")
                logger.info("✅ Table creation permissions OK")
            except Exception as e:
                logger.error(f"❌ Table creation failed: {e}")
                validation_ok = False
            
            cursor.close()
            conn.close()
            
            return validation_ok
            
        except Exception as e:
            logger.error(f"❌ Validation failed: {e}")
            return False
    
    def setup_complete_database(self, database_name, username="jirauser", password="mypassword"):
        """Complete database setup process"""
        logger.info("🚀 COMPLETE DATABASE SETUP")
        logger.info("="*50)
        logger.info(f"Database: {database_name}")
        logger.info(f"User: {username}")
        logger.info("="*50)
        
        steps = [
            ("Creating user", lambda: self.create_user(username, password)),
            ("Creating database", lambda: self.create_database(database_name, username)),
            ("Setting up extensions", lambda: self.setup_database_extensions(database_name)),
            ("Creating schemas", lambda: self.create_schemas(database_name, username)),
            ("Validating setup", lambda: self.validate_setup(database_name, username))
        ]
        
        for step_name, step_func in steps:
            logger.info(f"\n📋 {step_name}...")
            if not step_func():
                logger.error(f"❌ Failed: {step_name}")
                return False
        
        logger.info("\n" + "="*50)
        logger.info("✅ DATABASE SETUP COMPLETE!")
        logger.info("="*50)
        logger.info(f"🎯 Ready for ETL deployment to: {database_name}")
        
        return True

def main():
    """Interactive database setup"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Setup database for Jira Analytics ETL')
    parser.add_argument('--database', required=True, help='Target database name')
    parser.add_argument('--user', default='jirauser', help='Database user (default: jirauser)')
    parser.add_argument('--password', default='mypassword', help='Database password')
    parser.add_argument('--admin-user', default='postgres', help='Admin user (default: postgres)')
    parser.add_argument('--admin-password', default='postgres', help='Admin password')
    parser.add_argument('--host', default='localhost', help='Database host')
    parser.add_argument('--port', type=int, default=5432, help='Database port')
    
    args = parser.parse_args()
    
    setup = DatabaseSetup(args.host, args.port, args.admin_user, args.admin_password)
    
    success = setup.setup_complete_database(args.database, args.user, args.password)
    
    if success:
        print(f"\n🎉 Database '{args.database}' is ready for ETL deployment!")
        print(f"Next step: python deploy_etl_pipeline.py --database {args.database}")
        return 0
    else:
        print(f"\n❌ Database setup failed for '{args.database}'")
        return 1

if __name__ == "__main__":
    exit(main())
