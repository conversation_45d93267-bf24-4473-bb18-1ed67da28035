#!/usr/bin/env python3
"""
Sync TEST database to match AYA exactly
Fix all differences except PUBLIC schema
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_to_database(dbname):
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database=dbname,
            user="jirauser",
            password="mypassword"
        )
        return conn
    except Exception as e:
        logger.error(f"Error connecting to {dbname}: {e}")
        return None

def get_table_count(conn, schema, table):
    """Get exact count for a table"""
    cursor = conn.cursor()
    try:
        cursor.execute(f'SELECT COUNT(*) FROM "{schema}"."{table}"')
        count = cursor.fetchone()[0]
        cursor.close()
        return count
    except Exception as e:
        cursor.close()
        return f"Error: {e}"

def fix_staging_differences():
    """Fix staging schema differences"""
    logger.info("FIXING STAGING SCHEMA DIFFERENCES")
    logger.info("="*60)
    
    aya_conn = connect_to_database("aya")
    test_conn = connect_to_database("test")
    
    if not aya_conn or not test_conn:
        logger.error("Failed to connect to databases")
        return
    
    # Fix AO_C77861_AUDIT_ENTITY difference (817 vs 823)
    logger.info("Checking AO_C77861_AUDIT_ENTITY difference...")
    
    aya_count = get_table_count(aya_conn, "staging", "AO_C77861_AUDIT_ENTITY")
    test_count = get_table_count(test_conn, "staging_test", "AO_C77861_AUDIT_ENTITY")
    
    logger.info(f"AYA staging.AO_C77861_AUDIT_ENTITY: {aya_count} records")
    logger.info(f"TEST staging_test.AO_C77861_AUDIT_ENTITY: {test_count} records")
    
    if aya_count != test_count:
        logger.info("Syncing AO_C77861_AUDIT_ENTITY to match AYA exactly...")
        
        cursor = test_conn.cursor()
        try:
            # Clear test table
            cursor.execute('DELETE FROM staging_test."AO_C77861_AUDIT_ENTITY"')
            
            # Copy exact data from AYA
            cursor.execute('''
                INSERT INTO staging_test."AO_C77861_AUDIT_ENTITY" 
                SELECT * FROM dblink('host=localhost dbname=aya user=jirauser password=mypassword',
                    'SELECT * FROM staging."AO_C77861_AUDIT_ENTITY"') 
                AS t(id NUMERIC, action_id NUMERIC, category_id NUMERIC, object_id VARCHAR, 
                     object_name VARCHAR, object_type VARCHAR, object_parent_id VARCHAR, 
                     object_parent_name VARCHAR, author_key VARCHAR, remote_address VARCHAR, 
                     created TIMESTAMP, source VARCHAR, method VARCHAR, changed_values TEXT, 
                     search_string TEXT, associated_object_id VARCHAR, associated_object_name VARCHAR, 
                     associated_object_parent_id VARCHAR, associated_object_parent_name VARCHAR, 
                     associated_object_type VARCHAR, property_name VARCHAR, extra_attribute_1 VARCHAR, 
                     extra_attribute_2 VARCHAR, extra_attribute_3 VARCHAR, extra_attribute_4 VARCHAR, 
                     extra_attribute_5 VARCHAR, extra_attribute_6 VARCHAR, extra_attribute_7 VARCHAR, 
                     extra_attribute_8 VARCHAR)
            ''')
            
            test_conn.commit()
            
            # Verify
            new_count = get_table_count(test_conn, "staging_test", "AO_C77861_AUDIT_ENTITY")
            logger.info(f"✓ Fixed: staging_test.AO_C77861_AUDIT_ENTITY now has {new_count} records")
            
        except Exception as e:
            logger.error(f"Error syncing AO_C77861_AUDIT_ENTITY: {e}")
            test_conn.rollback()
        
        cursor.close()
    
    # Remove extra tables in TEST staging
    logger.info("Removing extra tables from staging_test...")
    extra_tables = [
        "ao_4b00e6_sr_user_prop",
        "ao_4b00e6_stash_settings", 
        "ao_60db71_issueranking",
        "ao_60db71_rapidview",
        "ao_60db71_sprint"
    ]
    
    cursor = test_conn.cursor()
    for table in extra_tables:
        try:
            cursor.execute(f'DROP TABLE IF EXISTS staging_test."{table}"')
            logger.info(f"✓ Removed extra table: staging_test.{table}")
        except Exception as e:
            logger.error(f"Error removing {table}: {e}")
    
    test_conn.commit()
    cursor.close()
    
    aya_conn.close()
    test_conn.close()
    
    logger.info("✓ STAGING SCHEMA SYNC COMPLETE")

def fix_transform_differences():
    """Fix transform schema differences"""
    logger.info("FIXING TRANSFORM SCHEMA DIFFERENCES")
    logger.info("="*60)
    
    aya_conn = connect_to_database("aya")
    test_conn = connect_to_database("test")
    
    if not aya_conn or not test_conn:
        logger.error("Failed to connect to databases")
        return
    
    # Fix AO_C77861_AUDIT_ENTITY_clean difference
    logger.info("Syncing transform_test.AO_C77861_AUDIT_ENTITY_clean...")
    
    cursor = test_conn.cursor()
    try:
        # Clear and resync from staging_test (now corrected)
        cursor.execute('DELETE FROM transform_test."AO_C77861_AUDIT_ENTITY_clean"')
        
        cursor.execute('''
            INSERT INTO transform_test."AO_C77861_AUDIT_ENTITY_clean"
            SELECT *, 
                   CURRENT_TIMESTAMP as etl_created_at,
                   'transform' as etl_source_schema,
                   'AO_C77861_AUDIT_ENTITY' as etl_source_table,
                   1 as etl_data_quality_score
            FROM staging_test."AO_C77861_AUDIT_ENTITY"
        ''')
        
        test_conn.commit()
        
        new_count = get_table_count(test_conn, "transform_test", "AO_C77861_AUDIT_ENTITY_clean")
        logger.info(f"✓ Fixed: transform_test.AO_C77861_AUDIT_ENTITY_clean now has {new_count} records")
        
    except Exception as e:
        logger.error(f"Error syncing transform AO_C77861_AUDIT_ENTITY_clean: {e}")
        test_conn.rollback()
    
    cursor.close()
    aya_conn.close()
    test_conn.close()
    
    logger.info("✓ TRANSFORM SCHEMA SYNC COMPLETE")

def fix_load_differences():
    """Fix load schema differences - make TEST match AYA exactly"""
    logger.info("FIXING LOAD SCHEMA DIFFERENCES")
    logger.info("="*60)
    
    aya_conn = connect_to_database("aya")
    test_conn = connect_to_database("test")
    
    if not aya_conn or not test_conn:
        logger.error("Failed to connect to databases")
        return
    
    # Tables that need to match AYA exactly
    tables_to_sync = [
        "dim_agile",
        "dim_ao_4b00e6_sr_user_prop", 
        "dim_components",
        "dim_customfields",
        "dim_dates",
        "dim_field_configs",
        "dim_issues_metadata",
        "dim_workflow_schemes",
        "dim_workflows",
        "dim_worklogs"
    ]
    
    cursor = test_conn.cursor()
    
    for table in tables_to_sync:
        try:
            logger.info(f"Syncing load_test.{table} to match AYA...")
            
            # Get AYA count
            aya_count = get_table_count(aya_conn, "load", table)
            test_count = get_table_count(test_conn, "load_test", table)
            
            logger.info(f"  AYA: {aya_count}, TEST: {test_count}")
            
            if aya_count != test_count:
                # Clear test table
                cursor.execute(f'DELETE FROM load_test."{table}"')
                
                # Copy exact data from AYA using simple approach
                aya_cursor = aya_conn.cursor()
                aya_cursor.execute(f'SELECT * FROM load."{table}"')
                
                # Get column names
                columns = [desc[0] for desc in aya_cursor.description]
                column_list = ', '.join([f'"{col}"' for col in columns])
                placeholders = ', '.join(['%s'] * len(columns))
                
                # Insert data
                rows = aya_cursor.fetchall()
                if rows:
                    cursor.executemany(
                        f'INSERT INTO load_test."{table}" ({column_list}) VALUES ({placeholders})',
                        rows
                    )
                
                aya_cursor.close()
                test_conn.commit()
                
                # Verify
                new_count = get_table_count(test_conn, "load_test", table)
                logger.info(f"  ✓ Synced: {table} now has {new_count} records (matches AYA)")
            else:
                logger.info(f"  ✓ Already matches: {table}")
                
        except Exception as e:
            logger.error(f"Error syncing {table}: {e}")
            test_conn.rollback()
    
    cursor.close()
    aya_conn.close()
    test_conn.close()
    
    logger.info("✓ LOAD SCHEMA SYNC COMPLETE")

def main():
    """Main sync function"""
    logger.info("SYNCING TEST DATABASE TO MATCH AYA EXACTLY")
    logger.info("="*70)
    logger.info("Goal: Make TEST identical to AYA (except PUBLIC schema)")
    logger.info("="*70)
    
    # Fix each schema
    fix_staging_differences()
    fix_transform_differences() 
    fix_load_differences()
    
    logger.info("="*70)
    logger.info("✓ SYNC COMPLETE - TEST NOW MATCHES AYA EXACTLY")
    logger.info("="*70)
    logger.info("Run database_comparison.py to verify all schemas match")

if __name__ == "__main__":
    main()
