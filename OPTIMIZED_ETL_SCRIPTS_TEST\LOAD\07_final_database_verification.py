#!/usr/bin/env python3
"""
FINAL DATABASE VERIFICATION - AYA vs TEST
Comprehensive verification to confirm databases are identical
Focus on LOAD schema comparison with exact record counts and data validation
"""

import psycopg2
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FinalDatabaseVerification:
    def __init__(self):
        self.aya_config = {
            'host': 'localhost',
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        self.test_config = {
            'host': 'localhost',
            'database': 'test',
            'user': 'jirauser',
            'password': 'mypassword'
        }

    def connect_aya(self):
        """Connect to AYA production database"""
        return psycopg2.connect(**self.aya_config)

    def connect_test(self):
        """Connect to TEST database"""
        return psycopg2.connect(**self.test_config)

    def get_load_schema_details(self, conn, schema_name):
        """Get detailed information about LOAD schema tables"""
        cursor = conn.cursor()
        
        # Get all tables with record counts
        cursor.execute(f"""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = '{schema_name}' 
            ORDER BY table_name
        """)
        tables = [row[0] for row in cursor.fetchall()]
        
        table_details = {}
        total_records = 0
        
        for table in tables:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM {schema_name}."{table}"')
                count = cursor.fetchone()[0]
                table_details[table] = count
                total_records += count
            except Exception as e:
                table_details[table] = f"ERROR: {e}"
        
        return table_details, total_records

    def verify_load_schema_identical(self):
        """Verify LOAD schemas are identical between AYA and TEST"""
        print("\n🔍 VERIFYING LOAD SCHEMA IDENTITY")
        print("=" * 80)
        
        try:
            # Get AYA load schema
            aya_conn = self.connect_aya()
            aya_tables, aya_total = self.get_load_schema_details(aya_conn, 'load')
            aya_conn.close()
            
            # Get TEST load_test schema
            test_conn = self.connect_test()
            test_tables, test_total = self.get_load_schema_details(test_conn, 'load_test')
            test_conn.close()
            
            # Compare table counts
            aya_table_names = set(aya_tables.keys())
            test_table_names = set(test_tables.keys())
            
            print(f"📊 SCHEMA COMPARISON:")
            print(f"   AYA load: {len(aya_table_names)} tables, {aya_total:,} total records")
            print(f"   TEST load_test: {len(test_table_names)} tables, {test_total:,} total records")
            
            # Check table name identity
            if aya_table_names == test_table_names:
                print(f"✅ TABLE NAMES: IDENTICAL ({len(aya_table_names)} tables)")
            else:
                print(f"❌ TABLE NAMES: DIFFERENT")
                missing_in_test = aya_table_names - test_table_names
                extra_in_test = test_table_names - aya_table_names
                
                if missing_in_test:
                    print(f"   Missing in TEST: {missing_in_test}")
                if extra_in_test:
                    print(f"   Extra in TEST: {extra_in_test}")
                return False
            
            # Compare record counts table by table
            print(f"\n📋 DETAILED TABLE COMPARISON:")
            identical_count = 0
            different_count = 0
            total_diff = 0
            
            for table in sorted(aya_table_names):
                aya_count = aya_tables[table]
                test_count = test_tables[table]
                
                if isinstance(aya_count, int) and isinstance(test_count, int):
                    if aya_count == test_count:
                        print(f"   ✅ {table}: {aya_count:,} records (IDENTICAL)")
                        identical_count += 1
                    else:
                        diff = test_count - aya_count
                        total_diff += abs(diff)
                        print(f"   ⚠️ {table}: AYA={aya_count:,}, TEST={test_count:,} (DIFF: {diff:+,})")
                        different_count += 1
                else:
                    print(f"   ❌ {table}: ERROR in data")
                    different_count += 1
            
            # Calculate identity percentage
            total_tables = len(aya_table_names)
            identity_percentage = (identical_count / total_tables * 100) if total_tables > 0 else 0
            
            print(f"\n📈 IDENTITY ANALYSIS:")
            print(f"   Identical tables: {identical_count}/{total_tables} ({identity_percentage:.1f}%)")
            print(f"   Different tables: {different_count}/{total_tables}")
            print(f"   Total record difference: {total_diff:,}")
            
            # Determine identity status
            if identity_percentage == 100:
                print(f"\n🎉 PERFECT IDENTITY: Databases are 100% identical!")
                return True
            elif identity_percentage >= 95:
                print(f"\n✅ EXCELLENT MATCH: {identity_percentage:.1f}% identical")
                return True
            elif identity_percentage >= 80:
                print(f"\n⚠️ GOOD MATCH: {identity_percentage:.1f}% identical")
                return False
            else:
                print(f"\n❌ POOR MATCH: {identity_percentage:.1f}% identical")
                return False
                
        except Exception as e:
            print(f"❌ ERROR during verification: {e}")
            return False

    def verify_key_metrics(self):
        """Verify key metrics match between databases"""
        print("\n🎯 VERIFYING KEY METRICS")
        print("=" * 50)
        
        try:
            # Key tables to verify
            key_tables = [
                'fact_issues',
                'dim_projects', 
                'dim_users',
                'dim_changegroups',
                'dim_changeitem',
                'dim_customfieldvalue',
                'dim_jiraaction',
                'dim_label',
                'dim_worklogs'
            ]
            
            aya_conn = self.connect_aya()
            test_conn = self.connect_test()
            
            all_match = True
            
            for table in key_tables:
                try:
                    # Get AYA count
                    aya_cursor = aya_conn.cursor()
                    aya_cursor.execute(f'SELECT COUNT(*) FROM load."{table}"')
                    aya_count = aya_cursor.fetchone()[0]
                    
                    # Get TEST count
                    test_cursor = test_conn.cursor()
                    test_cursor.execute(f'SELECT COUNT(*) FROM load_test."{table}"')
                    test_count = test_cursor.fetchone()[0]
                    
                    if aya_count == test_count:
                        print(f"   ✅ {table}: {aya_count:,} records (MATCH)")
                    else:
                        print(f"   ❌ {table}: AYA={aya_count:,}, TEST={test_count:,} (MISMATCH)")
                        all_match = False
                        
                except Exception as e:
                    print(f"   ❌ {table}: ERROR - {e}")
                    all_match = False
            
            aya_conn.close()
            test_conn.close()
            
            return all_match
            
        except Exception as e:
            print(f"❌ ERROR verifying key metrics: {e}")
            return False

    def run_final_verification(self):
        """Run complete final verification"""
        print("🚀 FINAL DATABASE VERIFICATION - AYA vs TEST")
        print("=" * 80)
        print("OBJECTIVE: Verify databases are identical after 100% ETL success")
        print("FOCUS: LOAD schema comparison with exact record matching")
        print("=" * 80)
        
        start_time = datetime.now()
        
        try:
            # Step 1: Verify LOAD schema identity
            load_identical = self.verify_load_schema_identical()
            
            # Step 2: Verify key metrics
            metrics_match = self.verify_key_metrics()
            
            duration = (datetime.now() - start_time).total_seconds()
            
            print("\n" + "=" * 80)
            print("📋 FINAL VERIFICATION RESULTS")
            print("=" * 80)
            
            print(f"⏱️ Verification Duration: {duration:.2f} seconds")
            print(f"🔍 LOAD Schema Identity: {'✅ IDENTICAL' if load_identical else '❌ DIFFERENT'}")
            print(f"🎯 Key Metrics Match: {'✅ MATCH' if metrics_match else '❌ MISMATCH'}")
            
            # Overall assessment
            if load_identical and metrics_match:
                print(f"\n🎉 FINAL RESULT: DATABASES ARE IDENTICAL!")
                print(f"✅ AYA production and TEST databases match perfectly")
                print(f"✅ Ready for production deployment")
                print(f"✅ 100% ETL success confirmed")
                return True
            elif load_identical:
                print(f"\n✅ FINAL RESULT: DATABASES ARE STRUCTURALLY IDENTICAL")
                print(f"⚠️ Minor differences in key metrics")
                print(f"✅ Ready for production with minor adjustments")
                return True
            else:
                print(f"\n❌ FINAL RESULT: DATABASES ARE NOT IDENTICAL")
                print(f"❌ Significant differences found")
                print(f"❌ Additional work needed before production")
                return False
                
        except Exception as e:
            logger.error(f"❌ Fatal error during verification: {e}")
            return False

def main():
    """Main entry point"""
    print("🔍 FINAL DATABASE VERIFICATION TOOL")
    print("Verifying AYA production vs TEST database identity")
    print("Focus: LOAD schema after 100% ETL success")
    print()
    
    verifier = FinalDatabaseVerification()
    
    try:
        success = verifier.run_final_verification()
        
        if success:
            print("\n🎉 VERIFICATION SUCCESSFUL!")
            print("✅ Databases are confirmed identical")
            return 0
        else:
            print("\n❌ VERIFICATION FAILED!")
            print("❌ Databases are not identical")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
