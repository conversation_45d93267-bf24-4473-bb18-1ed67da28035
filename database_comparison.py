#!/usr/bin/env python3
"""
Database Schema Comparison Tool
Compares AYA production database with TEST database
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import sys

def connect_to_database(dbname):
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database=dbname,
            user="jirauser",
            password="mypassword"
        )
        return conn
    except Exception as e:
        print(f"Error connecting to {dbname}: {e}")
        return None

def get_schema_tables(conn, schema_name):
    """Get all tables in a schema with row counts"""
    cursor = conn.cursor(cursor_factory=RealDictCursor)

    # Get table names
    cursor.execute("""
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = %s
        ORDER BY table_name
    """, (schema_name,))

    tables = cursor.fetchall()
    table_info = {}

    for table in tables:
        table_name = table['table_name']
        try:
            # Get row count with proper quoting for case-sensitive names
            cursor.execute(f'SELECT COUNT(*) as count FROM "{schema_name}"."{table_name}"')
            count = cursor.fetchone()['count']
            table_info[table_name] = count
        except Exception as e:
            # Rollback transaction on error and try again
            conn.rollback()
            try:
                cursor.execute(f'SELECT COUNT(*) as count FROM {schema_name}.{table_name}')
                count = cursor.fetchone()['count']
                table_info[table_name] = count
            except Exception as e2:
                conn.rollback()
                table_info[table_name] = f"Error: {str(e2)[:50]}..."

    cursor.close()
    return table_info



def main():
    print("DATABASE SCHEMA COMPARISON")
    print("AYA (Production) vs TEST Database")
    print("="*50)
    
    # First, let's check what databases exist
    print("Checking available databases...")
    temp_conn = connect_to_database("postgres")
    if temp_conn:
        cursor = temp_conn.cursor()
        cursor.execute("SELECT datname FROM pg_database WHERE datistemplate = false ORDER BY datname")
        databases = cursor.fetchall()
        print("Available databases:")
        for db in databases:
            print(f"  - {db[0]}")
        cursor.close()
        temp_conn.close()

    # Connect to both databases
    aya_conn = connect_to_database("aya")

    # Try different possible test database names
    test_conn = None
    test_db_names = ["test", "eya1", "staging_test", "transform_test", "load_test"]

    for db_name in test_db_names:
        test_conn = connect_to_database(db_name)
        if test_conn:
            print(f"Connected to test database: {db_name}")
            break

    if not test_conn:
        print("Trying to find test database with schemas...")
        # Check if any database has staging_test, transform_test, or load_test schemas
        for db_name in ["eya1", "test"]:
            temp_conn = connect_to_database(db_name)
            if temp_conn:
                cursor = temp_conn.cursor()
                cursor.execute("SELECT schema_name FROM information_schema.schemata WHERE schema_name LIKE '%test%'")
                test_schemas = cursor.fetchall()
                if test_schemas:
                    print(f"Found test schemas in {db_name}: {[s[0] for s in test_schemas]}")
                    test_conn = temp_conn
                    break
                cursor.close()
                temp_conn.close()
    
    if not aya_conn:
        print("Failed to connect to AYA database")
        return
    
    if not test_conn:
        print("Failed to connect to TEST database")
        return
    
    # Compare each schema - check both regular and test schemas
    schema_pairs = [
        ('public', 'public'),
        ('staging', 'staging_test'),
        ('transform', 'transform_test'),
        ('load', 'load_test')
    ]

    for aya_schema, test_schema in schema_pairs:
        try:
            print(f"\n{'='*60}")
            print(f"COMPARING: AYA.{aya_schema} vs TEST.{test_schema}")
            print(f"{'='*60}")

            # Get tables from both schemas
            aya_tables = get_schema_tables(aya_conn, aya_schema)
            test_tables = get_schema_tables(test_conn, test_schema)

            print(f"\nAYA Database - {aya_schema} schema: {len(aya_tables)} tables")
            print(f"TEST Database - {test_schema} schema: {len(test_tables)} tables")

            # Tables only in AYA
            aya_only = set(aya_tables.keys()) - set(test_tables.keys())
            if aya_only:
                print(f"\nTables ONLY in AYA {aya_schema}:")
                for table in sorted(aya_only):
                    print(f"  - {table} ({aya_tables[table]} rows)")

            # Tables only in TEST
            test_only = set(test_tables.keys()) - set(aya_tables.keys())
            if test_only:
                print(f"\nTables ONLY in TEST {test_schema}:")
                for table in sorted(test_only):
                    print(f"  - {table} ({test_tables[table]} rows)")

            # Common tables with different row counts
            common_tables = set(aya_tables.keys()) & set(test_tables.keys())
            if common_tables:
                print(f"\nCommon tables (AYA vs TEST row counts):")
                for table in sorted(common_tables):
                    aya_count = aya_tables[table]
                    test_count = test_tables[table]
                    status = "✓" if aya_count == test_count else "✗"
                    print(f"  {status} {table}: {aya_count} vs {test_count}")

        except Exception as e:
            print(f"\nError comparing {aya_schema} vs {test_schema}: {e}")
    
    # Close connections
    aya_conn.close()
    test_conn.close()
    
    print(f"\n{'='*60}")
    print("COMPARISON COMPLETE")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
