#!/usr/bin/env python3
"""
Database Schema Comparison Tool
Compares AYA production database with TEST database
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import sys

def connect_to_database(dbname):
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database=dbname,
            user="jirauser",
            password="mypassword"
        )
        return conn
    except Exception as e:
        print(f"Error connecting to {dbname}: {e}")
        return None

def get_schema_tables(conn, schema_name):
    """Get all tables in a schema with row counts"""
    cursor = conn.cursor(cursor_factory=RealDictCursor)

    # Get table names
    cursor.execute("""
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = %s
        ORDER BY table_name
    """, (schema_name,))

    tables = cursor.fetchall()
    table_info = {}

    for table in tables:
        table_name = table['table_name']
        try:
            # Get row count with proper quoting for case-sensitive names
            cursor.execute(f'SELECT COUNT(*) as count FROM "{schema_name}"."{table_name}"')
            count = cursor.fetchone()['count']
            table_info[table_name] = count
        except Exception as e:
            # Rollback transaction on error and try again
            conn.rollback()
            try:
                cursor.execute(f'SELECT COUNT(*) as count FROM {schema_name}.{table_name}')
                count = cursor.fetchone()['count']
                table_info[table_name] = count
            except Exception as e2:
                conn.rollback()
                table_info[table_name] = f"Error: {str(e2)[:50]}..."

    cursor.close()
    return table_info

def compare_schemas(aya_conn, test_conn, schema_name):
    """Compare a specific schema between AYA and TEST databases"""
    print(f"\n{'='*60}")
    print(f"COMPARING SCHEMA: {schema_name.upper()}")
    print(f"{'='*60}")
    
    # Get tables from both databases
    aya_tables = get_schema_tables(aya_conn, schema_name)
    test_tables = get_schema_tables(test_conn, schema_name)
    
    # Find all unique table names
    all_tables = set(aya_tables.keys()) | set(test_tables.keys())
    
    print(f"\nAYA Database - {schema_name} schema: {len(aya_tables)} tables")
    print(f"TEST Database - {schema_name} schema: {len(test_tables)} tables")
    
    # Tables only in AYA
    aya_only = set(aya_tables.keys()) - set(test_tables.keys())
    if aya_only:
        print(f"\nTables ONLY in AYA {schema_name}:")
        for table in sorted(aya_only):
            print(f"  - {table} ({aya_tables[table]} rows)")
    
    # Tables only in TEST
    test_only = set(test_tables.keys()) - set(aya_tables.keys())
    if test_only:
        print(f"\nTables ONLY in TEST {schema_name}:")
        for table in sorted(test_only):
            print(f"  - {table} ({test_tables[table]} rows)")
    
    # Common tables with different row counts
    common_tables = set(aya_tables.keys()) & set(test_tables.keys())
    if common_tables:
        print(f"\nCommon tables in {schema_name} (AYA vs TEST row counts):")
        for table in sorted(common_tables):
            aya_count = aya_tables[table]
            test_count = test_tables[table]
            status = "✓" if aya_count == test_count else "✗"
            print(f"  {status} {table}: {aya_count} vs {test_count}")

def main():
    print("DATABASE SCHEMA COMPARISON")
    print("AYA (Production) vs TEST Database")
    print("="*50)
    
    # Connect to both databases
    aya_conn = connect_to_database("aya")
    test_conn = connect_to_database("test")
    
    if not aya_conn:
        print("Failed to connect to AYA database")
        return
    
    if not test_conn:
        print("Failed to connect to TEST database")
        return
    
    # Compare each schema
    schemas = ['public', 'staging', 'transform', 'load']
    
    for schema in schemas:
        try:
            compare_schemas(aya_conn, test_conn, schema)
        except Exception as e:
            print(f"\nError comparing {schema} schema: {e}")
    
    # Close connections
    aya_conn.close()
    test_conn.close()
    
    print(f"\n{'='*60}")
    print("COMPARISON COMPLETE")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
