#!/usr/bin/env python3
"""
🔍 CHECK COMPLETE ETL STATUS
Comprehensive analysis of all 3 ETL schemas and data flow
"""

import psycopg2
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_dw():
    """Connexion au Data Warehouse"""
    return psycopg2.connect(
        host='localhost',
        database='aya',
        user='jirauser',
        password='mypassword'
    )

def check_schema_status(schema_name):
    """Vérifier le statut d'un schéma"""
    logger.info(f"🔍 ANALYSE SCHÉMA: {schema_name.upper()}")
    
    conn = connect_dw()
    cursor = conn.cursor()
    
    try:
        # Compter les tables et records
        cursor.execute(f"""
            SELECT 
                table_name,
                (xpath('/row/cnt/text()', xml_count))[1]::text::int as row_count
            FROM (
                SELECT 
                    table_name,
                    query_to_xml(format('SELECT COUNT(*) as cnt FROM {schema_name}.%I', table_name), false, true, '') as xml_count
                FROM information_schema.tables 
                WHERE table_schema = '{schema_name}'
                ORDER BY table_name
            ) t
        """)
        
        results = cursor.fetchall()
        
        total_tables = len(results)
        total_records = 0
        tables_with_data = 0
        empty_tables = 0
        
        logger.info(f"   📊 Tables trouvées: {total_tables}")
        
        for table_name, row_count in results:
            if row_count and row_count > 0:
                logger.info(f"      ✅ {table_name}: {row_count:,} records")
                total_records += row_count
                tables_with_data += 1
            else:
                logger.info(f"      ⚠️ {table_name}: VIDE")
                empty_tables += 1
        
        logger.info(f"   📈 Total records: {total_records:,}")
        logger.info(f"   ✅ Tables avec données: {tables_with_data}")
        logger.info(f"   ⚠️ Tables vides: {empty_tables}")
        
        return {
            'schema': schema_name,
            'total_tables': total_tables,
            'tables_with_data': tables_with_data,
            'empty_tables': empty_tables,
            'total_records': total_records,
            'completion_rate': (tables_with_data / total_tables * 100) if total_tables > 0 else 0
        }
        
    except Exception as e:
        logger.error(f"   ❌ Erreur analyse {schema_name}: {e}")
        return None
    finally:
        conn.close()

def check_data_flow_integrity():
    """Vérifier l'intégrité du flux de données"""
    logger.info(f"\n🔄 VÉRIFICATION FLUX DE DONNÉES")
    
    conn = connect_dw()
    cursor = conn.cursor()
    
    try:
        # Vérifier quelques tables clés dans les 3 schémas
        key_tables = ['jiraissue', 'project', 'cwd_user', 'component', 'worklog']
        
        for table in key_tables:
            logger.info(f"   📊 Flux {table}:")
            
            # Staging
            try:
                cursor.execute(f'SELECT COUNT(*) FROM staging."{table}" WHERE instance_id = 1')
                staging_count = cursor.fetchone()[0]
                logger.info(f"      STAGING: {staging_count:,} records")
            except:
                staging_count = 0
                logger.info(f"      STAGING: N/A")
            
            # Transform
            try:
                cursor.execute(f'SELECT COUNT(*) FROM transform."{table}_clean" WHERE instance_id = 1')
                transform_count = cursor.fetchone()[0]
                logger.info(f"      TRANSFORM: {transform_count:,} records")
            except:
                transform_count = 0
                logger.info(f"      TRANSFORM: N/A")
            
            # Load (fact_issues pour jiraissue, dim_* pour les autres)
            if table == 'jiraissue':
                load_table = 'fact_issues'
            elif table == 'cwd_user':
                load_table = 'dim_users'
            elif table == 'project':
                load_table = 'dim_projects'
            else:
                load_table = f'dim_{table}'
            try:
                cursor.execute(f'SELECT COUNT(*) FROM load."{load_table}" WHERE instance_id = 1')
                load_count = cursor.fetchone()[0]
                logger.info(f"      LOAD: {load_count:,} records")
            except:
                load_count = 0
                logger.info(f"      LOAD: N/A")
            
            # Analyser la cohérence
            if staging_count > 0 and transform_count > 0 and load_count > 0:
                logger.info(f"      ✅ Flux complet")
            elif staging_count > 0 and transform_count > 0:
                logger.info(f"      ⚠️ Manque LOAD")
            elif staging_count > 0:
                logger.info(f"      ⚠️ Manque TRANSFORM + LOAD")
            else:
                logger.info(f"      ❌ Pas de données")
        
    except Exception as e:
        logger.error(f"   ❌ Erreur vérification flux: {e}")
    finally:
        conn.close()

def main():
    """Point d'entrée principal"""
    print("🔍 CHECK COMPLETE ETL STATUS")
    print("=" * 70)
    print("🎯 Objectif: Analyse complète des 3 schémas ETL")
    print("=" * 70)
    
    # Analyser chaque schéma
    staging_status = check_schema_status('staging')
    transform_status = check_schema_status('transform')
    load_status = check_schema_status('load')
    
    # Vérifier le flux de données
    check_data_flow_integrity()
    
    # Rapport final
    logger.info(f"\n📊 RAPPORT FINAL ETL")
    logger.info("=" * 70)
    
    if staging_status:
        logger.info(f"📥 STAGING: {staging_status['tables_with_data']}/{staging_status['total_tables']} tables ({staging_status['completion_rate']:.1f}%) - {staging_status['total_records']:,} records")
    
    if transform_status:
        logger.info(f"🔄 TRANSFORM: {transform_status['tables_with_data']}/{transform_status['total_tables']} tables ({transform_status['completion_rate']:.1f}%) - {transform_status['total_records']:,} records")
    
    if load_status:
        logger.info(f"📊 LOAD: {load_status['tables_with_data']}/{load_status['total_tables']} tables ({load_status['completion_rate']:.1f}%) - {load_status['total_records']:,} records")
    
    # Évaluation globale
    if all([staging_status, transform_status, load_status]):
        total_completion = (staging_status['completion_rate'] + transform_status['completion_rate'] + load_status['completion_rate']) / 3
        
        if total_completion >= 90:
            logger.info(f"\n🎉 ETL EXCELLENT: {total_completion:.1f}% completion")
            logger.info(f"   ✅ Pipeline ETL entièrement opérationnel")
        elif total_completion >= 70:
            logger.info(f"\n✅ ETL BON: {total_completion:.1f}% completion")
            logger.info(f"   ✅ Pipeline ETL largement fonctionnel")
        else:
            logger.warning(f"\n⚠️ ETL PARTIEL: {total_completion:.1f}% completion")
            logger.warning(f"   Améliorations nécessaires")
    
    return 0

if __name__ == "__main__":
    exit(main())
