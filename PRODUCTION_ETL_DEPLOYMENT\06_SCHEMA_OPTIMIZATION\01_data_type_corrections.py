#!/usr/bin/env python3
"""
🔧 DATA TYPE CORRECTIONS
Fix data type inconsistencies in the star schema

FIXES:
1. VARCHAR columns that should be NUMERIC
2. TEXT columns that need VARCHAR limits
3. Timestamp consistency issues
4. ID column standardization
"""

import psycopg2
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_to_database(dbname="aya"):
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database=dbname,
            user="jirauser",
            password="mypassword"
        )
        return conn
    except Exception as e:
        logger.error(f"Error connecting to {dbname}: {e}")
        return None

def fix_varchar_id_columns():
    """Fix VARCHAR columns that should be NUMERIC"""
    logger.info("🔧 FIXING VARCHAR ID COLUMNS TO NUMERIC")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    # VARCHAR ID columns that should be NUMERIC
    varchar_id_fixes = [
        ("dim_field_configs", "fieldconfigscheme_fieldid", "NUMERIC"),
        ("dim_field_configs", "fieldid", "NUMERIC"),
        ("dim_issues_metadata", "id", "NUMERIC"),
        ("dim_issues_metadata", "issuestatus_id", "NUMERIC"),
        ("dim_issues_metadata", "priority_id", "NUMERIC"),
        ("dim_issues_metadata", "resolution_id", "NUMERIC"),
        ("dim_managedconfigurationitem", "item_id", "NUMERIC"),
        ("dim_users", "external_id", "NUMERIC")
    ]
    
    cursor = conn.cursor()
    success_count = 0
    
    for table, column, new_type in varchar_id_fixes:
        try:
            logger.info(f"  🔧 {table}.{column} → {new_type}")
            
            # Check if column exists and is VARCHAR
            cursor.execute("""
                SELECT data_type FROM information_schema.columns 
                WHERE table_schema = 'load' AND table_name = %s AND column_name = %s
            """, (table, column))
            
            result = cursor.fetchone()
            if result and result[0] == 'character varying':
                # Alter column type with USING clause for safe conversion
                cursor.execute(f'''
                    ALTER TABLE load."{table}" 
                    ALTER COLUMN "{column}" TYPE {new_type} 
                    USING CASE 
                        WHEN "{column}" ~ '^[0-9]+$' THEN "{column}"::{new_type}
                        ELSE NULL 
                    END
                ''')
                
                conn.commit()
                logger.info(f"    ✅ Fixed: {table}.{column}")
                success_count += 1
            else:
                logger.info(f"    ⚠️ Skipped: {table}.{column} (not VARCHAR or doesn't exist)")
                
        except Exception as e:
            logger.error(f"    ❌ Error fixing {table}.{column}: {e}")
            conn.rollback()
    
    cursor.close()
    conn.close()
    
    logger.info(f"\n📊 VARCHAR ID FIXES: {success_count}/{len(varchar_id_fixes)} completed")
    return success_count == len(varchar_id_fixes)

def fix_text_columns():
    """Fix TEXT columns that should have VARCHAR limits"""
    logger.info("\n📝 FIXING TEXT COLUMNS TO VARCHAR")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    # TEXT columns that should be VARCHAR with appropriate limits
    text_column_fixes = [
        ("dim_agile", "ao_60db71_sprint_goal", "VARCHAR(1000)"),
        ("dim_ao_4b00e6_sr_user_prop", "property_value", "VARCHAR(2000)"),
        ("dim_ao_4b00e6_stash_settings", "setting", "VARCHAR(2000)"),
        ("dim_changeitem", "newstring", "VARCHAR(1000)"),
        ("dim_changeitem", "newvalue", "VARCHAR(1000)"),
        ("dim_changeitem", "oldstring", "VARCHAR(1000)"),
        ("dim_changeitem", "oldvalue", "VARCHAR(1000)"),
        ("dim_components", "description", "VARCHAR(2000)"),
        ("dim_customfields", "description", "VARCHAR(2000)"),
        ("dim_data_quality", "description", "VARCHAR(500)"),
        ("dim_field_configs", "description", "VARCHAR(1000)"),
        ("dim_field_configs", "fieldconfigscheme_description", "VARCHAR(1000)"),
        ("dim_issues_metadata", "description", "VARCHAR(2000)"),
        ("dim_issues_metadata", "issuestatus_description", "VARCHAR(500)"),
        ("dim_issues_metadata", "priority_description", "VARCHAR(500)"),
        ("dim_issues_metadata", "resolution_description", "VARCHAR(500)"),
        ("dim_jiraaction", "actionbody", "VARCHAR(5000)"),
        ("dim_permissions", "description", "VARCHAR(1000)"),
        ("dim_projectcategory", "description", "VARCHAR(2000)"),
        ("dim_projects", "description", "VARCHAR(2000)"),
        ("dim_workflow_schemes", "description", "VARCHAR(2000)"),
        ("dim_workflows", "description", "VARCHAR(2000)"),
        ("dim_workflows", "workflow_descriptor", "VARCHAR(10000)"),
        ("dim_worklogs", "worklog_body", "VARCHAR(5000)"),
        ("fact_issues", "description", "VARCHAR(10000)"),
        ("fact_issues", "summary", "VARCHAR(1000)")
    ]
    
    cursor = conn.cursor()
    success_count = 0
    
    for table, column, new_type in text_column_fixes:
        try:
            logger.info(f"  📝 {table}.{column} → {new_type}")
            
            # Check if column exists and is TEXT
            cursor.execute("""
                SELECT data_type FROM information_schema.columns 
                WHERE table_schema = 'load' AND table_name = %s AND column_name = %s
            """, (table, column))
            
            result = cursor.fetchone()
            if result and result[0] == 'text':
                # Alter column type
                cursor.execute(f'''
                    ALTER TABLE load."{table}" 
                    ALTER COLUMN "{column}" TYPE {new_type}
                ''')
                
                conn.commit()
                logger.info(f"    ✅ Fixed: {table}.{column}")
                success_count += 1
            else:
                logger.info(f"    ⚠️ Skipped: {table}.{column} (not TEXT or doesn't exist)")
                
        except Exception as e:
            logger.error(f"    ❌ Error fixing {table}.{column}: {e}")
            conn.rollback()
    
    cursor.close()
    conn.close()
    
    logger.info(f"\n📊 TEXT COLUMN FIXES: {success_count}/{len(text_column_fixes)} completed")
    return success_count == len(text_column_fixes)

def standardize_timestamp_columns():
    """Standardize timestamp columns to consistent format"""
    logger.info("\n📅 STANDARDIZING TIMESTAMP COLUMNS")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    # Get all timestamp columns that are 'timestamp with time zone' and should be 'timestamp without time zone'
    cursor.execute("""
        SELECT table_name, column_name
        FROM information_schema.columns 
        WHERE table_schema = 'load'
        AND data_type = 'timestamp with time zone'
        AND (column_name LIKE '%_date' OR column_name LIKE '%created%' OR column_name LIKE '%updated%')
        ORDER BY table_name, column_name
    """)
    
    timestamp_columns = cursor.fetchall()
    success_count = 0
    
    for table_name, column_name in timestamp_columns:
        try:
            logger.info(f"  📅 {table_name}.{column_name} → TIMESTAMP WITHOUT TIME ZONE")
            
            cursor.execute(f'''
                ALTER TABLE load."{table_name}" 
                ALTER COLUMN "{column_name}" TYPE TIMESTAMP WITHOUT TIME ZONE
            ''')
            
            conn.commit()
            logger.info(f"    ✅ Fixed: {table_name}.{column_name}")
            success_count += 1
            
        except Exception as e:
            logger.error(f"    ❌ Error fixing {table_name}.{column_name}: {e}")
            conn.rollback()
    
    cursor.close()
    conn.close()
    
    logger.info(f"\n📊 TIMESTAMP FIXES: {success_count}/{len(timestamp_columns)} completed")
    return success_count == len(timestamp_columns)

def create_backup_before_changes():
    """Create backup of current schema before making changes"""
    logger.info("💾 CREATING BACKUP BEFORE DATA TYPE CHANGES")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        # Create backup schema
        backup_schema = f"load_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        cursor.execute(f'CREATE SCHEMA IF NOT EXISTS "{backup_schema}"')
        
        # Get all tables in load schema
        cursor.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'load' ORDER BY table_name
        """)
        
        tables = [row[0] for row in cursor.fetchall()]
        
        # Copy each table structure (not data, just for reference)
        for table in tables[:5]:  # Backup first 5 tables as sample
            cursor.execute(f'''
                CREATE TABLE "{backup_schema}"."{table}" 
                AS SELECT * FROM load."{table}" LIMIT 0
            ''')
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info(f"✅ Backup schema created: {backup_schema}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error creating backup: {e}")
        conn.rollback()
        cursor.close()
        conn.close()
        return False

def validate_data_type_fixes():
    """Validate that data type fixes were applied correctly"""
    logger.info("\n✅ VALIDATING DATA TYPE FIXES")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    # Check for remaining VARCHAR ID columns
    cursor.execute("""
        SELECT table_name, column_name, data_type
        FROM information_schema.columns 
        WHERE table_schema = 'load'
        AND data_type = 'character varying'
        AND (column_name LIKE '%_id' OR column_name LIKE '%id')
        ORDER BY table_name, column_name
    """)
    
    remaining_varchar_ids = cursor.fetchall()
    
    # Check for remaining TEXT columns
    cursor.execute("""
        SELECT table_name, column_name, data_type
        FROM information_schema.columns 
        WHERE table_schema = 'load'
        AND data_type = 'text'
        ORDER BY table_name, column_name
    """)
    
    remaining_text_cols = cursor.fetchall()
    
    cursor.close()
    conn.close()
    
    logger.info(f"📊 Remaining VARCHAR ID columns: {len(remaining_varchar_ids)}")
    logger.info(f"📊 Remaining TEXT columns: {len(remaining_text_cols)}")
    
    if len(remaining_varchar_ids) == 0 and len(remaining_text_cols) == 0:
        logger.info("✅ All data type fixes applied successfully!")
        return True
    else:
        logger.warning("⚠️ Some data type issues remain")
        return False

def main():
    """Main data type correction function"""
    logger.info("🔧 DATA TYPE CORRECTIONS")
    logger.info("="*70)
    logger.info("Fixing data type inconsistencies in star schema...")
    logger.info("="*70)
    
    start_time = datetime.now()
    
    # Step 1: Create backup
    if not create_backup_before_changes():
        logger.error("❌ Failed to create backup - aborting")
        return False
    
    # Step 2: Fix VARCHAR ID columns
    varchar_success = fix_varchar_id_columns()
    
    # Step 3: Fix TEXT columns
    text_success = fix_text_columns()
    
    # Step 4: Standardize timestamps
    timestamp_success = standardize_timestamp_columns()
    
    # Step 5: Validate fixes
    validation_success = validate_data_type_fixes()
    
    # Summary
    duration = datetime.now() - start_time
    logger.info("\n" + "="*70)
    logger.info("📊 DATA TYPE CORRECTION SUMMARY")
    logger.info("="*70)
    logger.info(f"⏱️ Duration: {duration}")
    logger.info(f"🔧 VARCHAR ID fixes: {'✅' if varchar_success else '❌'}")
    logger.info(f"📝 TEXT column fixes: {'✅' if text_success else '❌'}")
    logger.info(f"📅 Timestamp fixes: {'✅' if timestamp_success else '❌'}")
    logger.info(f"✅ Validation: {'✅' if validation_success else '❌'}")
    
    overall_success = varchar_success and text_success and timestamp_success and validation_success
    
    if overall_success:
        logger.info("\n🎉 ALL DATA TYPE CORRECTIONS COMPLETED SUCCESSFULLY!")
    else:
        logger.error("\n❌ Some data type corrections failed")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
