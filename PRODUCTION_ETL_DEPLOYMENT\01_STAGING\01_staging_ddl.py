#!/usr/bin/env python3
"""
🔧 RECONSTRUCTION SCHÉMA STAGING AVEC TYPES EXACTS
PHASE 1 de l'ETL: Création des tables staging avec types PostgreSQL EXACTS
Basé sur l'analyse des 52 tables critiques Jira

⚠️ ATTENTION: Types de colonnes à 1000% EXACTS avec la source !
"""

import psycopg2
import logging
from datetime import datetime
import json

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'staging_rebuild_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StagingSchemaBuilder:
    """
    Construction du schéma STAGING avec types PostgreSQL EXACTS
    """
    
    def __init__(self):
        # Configuration Data Warehouse
        self.dw_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # Configuration Jira (source)
        self.jira_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'jiradb',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # 52 tables critiques complètes
        self.all_critical_tables = [
            # 🔥 CORE_BUSINESS (12 tables) - PRIORITÉ ABSOLUE
            'jiraissue', 'project', 'component', 'projectversion',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelink', 'issuelinktype', 'label', 'nodeassociation',

            # 👥 USERS_GROUPS (8 tables) - CRITIQUE MIGRATION
            'cwd_user', 'cwd_group', 'cwd_membership', 'cwd_user_attributes',
            'userassociation', 'projectroleactor', 'app_user', 'cwd_directory',

            # 🔄 WORKFLOWS (5 tables) - COMPLEXITÉ MIGRATION
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',
            'os_currentstep', 'os_wfentry',

            # ⚙️ CONFIGURATION (6 tables)
            'fieldconfiguration', 'fieldconfigscheme', 'permissionscheme',
            'schemepermissions', 'fieldscreen', 'fieldscreentab',

            # 📝 CHANGES_HISTORY (3 tables) - AUDIT MIGRATION
            'changegroup', 'changeitem', 'jiraaction',

            # 🔧 PLUGINS_MANAGEMENT (2 tables)
            'pluginversion', 'managedconfigurationitem',

            # 📊 LOOKUP_TABLES (6 tables) - RÉFÉRENTIELS
            'issuestatus', 'priority', 'resolution', 'issuetype',
            'projectrole', 'projectcategory',

            # 🤖 SCRIPT_RUNNER (4 tables) - BLOQUEURS CLOUD
            'AO_4B00E6_SR_USER_PROP', 'AO_4B00E6_STASH_SETTINGS',
            'AO_4B00E6_UPGRADE_BACKUP', 'AO_786AC3_SQL_FAVOURITE',

            # 🎯 AGILE_BOARDS (3 tables) - MÉTHODOLOGIES
            'AO_60DB71_RAPIDVIEW', 'AO_60DB71_SPRINT', 'AO_60DB71_ISSUERANKING',

            # 🔍 JSM_AUDIT (3 tables) - SERVICE DESK
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]

        # Tables prioritaires déjà traitées (phase 1)
        self.phase1_tables = [
            'jiraissue', 'project', 'cwd_user', 'worklog', 'component',
            'customfield', 'customfieldvalue', 'changegroup', 'changeitem',
            'issuestatus', 'priority', 'resolution', 'issuetype'
        ]

        # Tables restantes à traiter (phase 2)
        self.phase2_tables = [table for table in self.all_critical_tables
                             if table not in self.phase1_tables]
        
        self.stats = {
            'start_time': datetime.now(),
            'tables_created': 0,
            'total_columns': 0,
            'errors': []
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = False
            return conn
        except Exception as e:
            logger.error(f"❌ Erreur connexion DW: {e}")
            raise
    
    def connect_jira(self):
        """Connexion READ-ONLY à Jira"""
        try:
            conn = psycopg2.connect(**self.jira_config)
            conn.set_session(readonly=True, autocommit=True)
            return conn
        except Exception as e:
            logger.error(f"❌ Erreur connexion Jira: {e}")
            raise
    
    def get_exact_column_types_from_jira(self, jira_cursor, table_name):
        """
        Récupérer les types de colonnes EXACTS directement depuis Jira
        Pour les tables non présentes dans TABLE_SCHEMAS_EXACTS
        """
        try:
            jira_cursor.execute("""
                SELECT
                    column_name,
                    data_type,
                    character_maximum_length,
                    numeric_precision,
                    numeric_scale,
                    is_nullable,
                    column_default,
                    ordinal_position
                FROM information_schema.columns
                WHERE table_schema = 'public' AND table_name = %s
                ORDER BY ordinal_position
            """, (table_name,))

            columns = jira_cursor.fetchall()

            if not columns:
                logger.warning(f"⚠️ Aucune colonne trouvée pour {table_name}")
                return None

            # Construire le DDL exact
            column_definitions = []

            for col_name, data_type, char_max_len, num_precision, num_scale, is_nullable, col_default, position in columns:
                # Construire le type exact
                if data_type == 'character varying':
                    if char_max_len:
                        pg_type = f'VARCHAR({char_max_len})'
                    else:
                        pg_type = 'TEXT'
                elif data_type == 'character':
                    if char_max_len:
                        pg_type = f'CHAR({char_max_len})'
                    else:
                        pg_type = 'CHAR(1)'
                elif data_type == 'text':
                    pg_type = 'TEXT'
                elif data_type == 'numeric':
                    if num_precision and num_scale is not None:
                        pg_type = f'NUMERIC({num_precision},{num_scale})'
                    elif num_precision:
                        pg_type = f'NUMERIC({num_precision})'
                    else:
                        pg_type = 'NUMERIC'
                elif data_type == 'integer':
                    pg_type = 'INTEGER'
                elif data_type == 'bigint':
                    pg_type = 'BIGINT'
                elif data_type == 'smallint':
                    pg_type = 'SMALLINT'
                elif data_type == 'double precision':
                    pg_type = 'DOUBLE PRECISION'
                elif data_type == 'real':
                    pg_type = 'REAL'
                elif data_type == 'timestamp with time zone':
                    pg_type = 'TIMESTAMPTZ'
                elif data_type == 'timestamp without time zone':
                    pg_type = 'TIMESTAMP'
                elif data_type == 'date':
                    pg_type = 'DATE'
                elif data_type == 'time with time zone':
                    pg_type = 'TIMETZ'
                elif data_type == 'time without time zone':
                    pg_type = 'TIME'
                elif data_type == 'boolean':
                    pg_type = 'BOOLEAN'
                elif data_type == 'bytea':
                    pg_type = 'BYTEA'
                else:
                    # Type non reconnu, utiliser TEXT par sécurité
                    pg_type = 'TEXT'
                    logger.warning(f"⚠️ Type non reconnu '{data_type}' pour {table_name}.{col_name}, utilisation TEXT")

                # Contrainte NULL/NOT NULL
                null_constraint = '' if is_nullable == 'YES' else ' NOT NULL'

                # Définition complète de la colonne
                column_def = f'    "{col_name}" {pg_type}{null_constraint}'
                column_definitions.append(column_def)

            logger.info(f"   ✅ {table_name}: {len(column_definitions)} colonnes depuis Jira")
            return column_definitions

        except Exception as e:
            logger.error(f"❌ Erreur récupération types pour {table_name}: {e}")
            return None

    def get_exact_column_types_from_schemas(self, table_name):
        """
        Récupérer les types de colonnes EXACTS depuis les schémas pré-analysés
        """
        # Import des schémas EXACTS depuis example/extract.py
        import sys
        import os

        try:
            # Ajouter le chemin vers example/
            example_path = os.path.join(os.path.dirname(__file__), 'example')
            if example_path not in sys.path:
                sys.path.insert(0, example_path)

            import extract
            TABLE_SCHEMAS_EXACTS = extract.TABLE_SCHEMAS_EXACTS

            if table_name in TABLE_SCHEMAS_EXACTS:
                schema_ddl = TABLE_SCHEMAS_EXACTS[table_name]

                # Parser le DDL pour extraire les définitions de colonnes
                lines = [line.strip() for line in schema_ddl.strip().split('\n') if line.strip()]
                column_definitions = []

                for line in lines:
                    # Ignorer les lignes spéciales
                    if line.startswith('PRIMARY KEY') or line.startswith('instance_id') or line.startswith('extracted_at'):
                        continue

                    # Nettoyer la ligne (enlever virgules finales)
                    clean_line = line.rstrip(',')
                    if clean_line and not clean_line.startswith('PRIMARY'):
                        column_definitions.append(f'    {clean_line}')

                logger.info(f"   ✅ {table_name}: {len(column_definitions)} colonnes depuis schémas EXACTS")
                return column_definitions

            else:
                return None  # Table non trouvée dans schémas pré-analysés

        except Exception as e:
            logger.error(f"❌ Erreur récupération schémas EXACTS pour {table_name}: {e}")
            return None
    
    def create_staging_schema(self):
        """Créer le schéma staging"""
        logger.info("🔧 Création du schéma STAGING")
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Créer le schéma staging
            cursor.execute('CREATE SCHEMA IF NOT EXISTS staging')
            dw_conn.commit()
            logger.info("✅ Schéma staging créé")
            
        except Exception as e:
            logger.error(f"❌ Erreur création schéma staging: {e}")
            dw_conn.rollback()
            raise
        finally:
            dw_conn.close()
    
    def create_staging_table(self, table_name, column_definitions):
        """
        Créer une table staging avec types EXACTS + colonnes ETL
        """
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Ajouter les colonnes ETL standard
            etl_columns = [
                '    instance_id INTEGER NOT NULL',
                '    extracted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
            ]
            
            all_columns = column_definitions + etl_columns
            columns_ddl = ',\n'.join(all_columns)
            
            # Créer la table
            create_sql = f'''
                CREATE TABLE staging."{table_name}" (
                {columns_ddl}
                )
            '''
            
            cursor.execute(f'DROP TABLE IF EXISTS staging."{table_name}" CASCADE')
            cursor.execute(create_sql)
            dw_conn.commit()
            
            self.stats['tables_created'] += 1
            self.stats['total_columns'] += len(column_definitions)
            
            logger.info(f"   ✅ staging.{table_name}: {len(column_definitions)} colonnes + 2 ETL")
            
        except Exception as e:
            logger.error(f"   ❌ Erreur création staging.{table_name}: {e}")
            dw_conn.rollback()
            self.stats['errors'].append(f"{table_name}: {e}")
            raise
        finally:
            dw_conn.close()
    
    def build_phase1_staging(self):
        """
        Construire les tables staging pour les 13 tables prioritaires (Phase 1)
        """
        logger.info("🚀 CONSTRUCTION STAGING - PHASE 1 (13 tables prioritaires)")
        logger.info("=" * 70)

        for i, table_name in enumerate(self.phase1_tables, 1):
            logger.info(f"🔧 [{i:2d}/13] Création staging.{table_name}")

            # Récupérer les types exacts depuis les schémas pré-analysés
            column_definitions = self.get_exact_column_types_from_schemas(table_name)

            if column_definitions:
                # Créer la table staging
                self.create_staging_table(table_name, column_definitions)
            else:
                logger.error(f"   ❌ Impossible de récupérer les colonnes pour {table_name}")
                self.stats['errors'].append(f"{table_name}: Colonnes non récupérées")

    def build_phase2_staging(self):
        """
        Construire les tables staging pour les 39 tables restantes (Phase 2)
        """
        logger.info(f"🚀 CONSTRUCTION STAGING - PHASE 2 ({len(self.phase2_tables)} tables restantes)")
        logger.info("=" * 70)

        jira_conn = self.connect_jira()
        jira_cursor = jira_conn.cursor()

        try:
            for i, table_name in enumerate(self.phase2_tables, 1):
                logger.info(f"🔧 [{i:2d}/{len(self.phase2_tables)}] Création staging.{table_name}")

                # D'abord essayer les schémas pré-analysés
                column_definitions = self.get_exact_column_types_from_schemas(table_name)

                # Si pas trouvé, récupérer directement depuis Jira
                if not column_definitions:
                    column_definitions = self.get_exact_column_types_from_jira(jira_cursor, table_name)

                if column_definitions:
                    # Créer la table staging
                    self.create_staging_table(table_name, column_definitions)
                else:
                    logger.error(f"   ❌ Impossible de récupérer les colonnes pour {table_name}")
                    self.stats['errors'].append(f"{table_name}: Colonnes non récupérées")

        finally:
            jira_conn.close()

    def build_all_staging_tables(self):
        """
        Construire toutes les tables staging (52 tables complètes)
        """
        logger.info("🚀 CONSTRUCTION STAGING COMPLÈTE - 52 TABLES CRITIQUES")
        logger.info("=" * 70)

        # Phase 1: Tables prioritaires (déjà créées, on skip)
        logger.info("📋 Phase 1: Vérification tables prioritaires existantes...")

        # Phase 2: Tables restantes
        self.build_phase2_staging()

        logger.info(f"\n✅ STAGING COMPLET: {len(self.all_critical_tables)} tables critiques")
    
    def verify_staging_schema(self):
        """Vérifier la création du schéma staging"""
        logger.info("\n🔍 VÉRIFICATION SCHÉMA STAGING")
        logger.info("=" * 50)
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            # Lister les tables créées
            cursor.execute("""
                SELECT table_name, 
                       (SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_schema = 'staging' AND table_name = t.table_name) as column_count
                FROM information_schema.tables t
                WHERE table_schema = 'staging'
                ORDER BY table_name
            """)
            
            staging_tables = cursor.fetchall()
            
            logger.info(f"📊 Tables staging créées: {len(staging_tables)}")
            
            for table_name, column_count in staging_tables:
                logger.info(f"   ✅ staging.{table_name}: {column_count} colonnes")
            
            return len(staging_tables)
            
        finally:
            dw_conn.close()

def main():
    """Point d'entrée principal"""
    print("🔧 RECONSTRUCTION SCHÉMA STAGING - PHASE 1")
    print("=" * 60)
    print("📋 13 tables prioritaires avec types EXACTS")
    print("🎯 Schéma: staging.*")
    print("=" * 60)
    
    builder = StagingSchemaBuilder()
    
    try:
        # Créer le schéma staging
        builder.create_staging_schema()
        
        # Construire les tables Phase 1
        builder.build_phase1_staging()
        
        # Vérifier la création
        tables_created = builder.verify_staging_schema()
        
        # Statistiques finales
        duration = (datetime.now() - builder.stats['start_time']).total_seconds()
        
        logger.info(f"\n🎉 SCHÉMA STAGING CRÉÉ!")
        logger.info(f"⏱️ Durée: {duration:.2f} secondes")
        logger.info(f"📊 Tables créées: {builder.stats['tables_created']}")
        logger.info(f"📈 Colonnes totales: {builder.stats['total_columns']}")
        logger.info(f"❌ Erreurs: {len(builder.stats['errors'])}")
        
        if builder.stats['errors']:
            logger.warning(f"🚨 Erreurs rencontrées:")
            for error in builder.stats['errors'][:3]:
                logger.warning(f"   - {error}")
        
        logger.info(f"\n🎯 PROCHAINE ÉTAPE:")
        logger.info(f"   Exécuter: python extract_to_staging.py")
        logger.info(f"   Pour extraire les données vers staging")
        
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
