#!/usr/bin/env python3
"""
🚀 EXTRACTION PHASE 2 - 39 TABLES RESTANTES
Extraction des données des 39 tables Phase 2 vers staging
Complète l'extraction pour avoir les 52 tables avec données

⚠️ ATTENTION: Extraction READ-ONLY depuis Jira vers Data Warehouse
"""

import psycopg2
import logging
from datetime import datetime
import time

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'extract_phase2_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class Phase2Extractor:
    """
    Extracteur pour les 39 tables Phase 2 restantes
    """
    
    def __init__(self):
        # Configuration Data Warehouse (destination)
        self.dw_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # Configuration Jira (source READ-ONLY)
        self.jira_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'jiradb',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # Tables Phase 1 déjà traitées (avec données)
        self.phase1_tables = [
            'jiraissue', 'project', 'cwd_user', 'worklog', 'component',
            'customfield', 'customfieldvalue', 'changegroup', 'changeitem',
            'issuestatus', 'priority', 'resolution', 'issuetype'
        ]
        
        # 52 tables critiques complètes
        self.all_critical_tables = [
            # 🔥 CORE_BUSINESS (12 tables)
            'jiraissue', 'project', 'component', 'projectversion',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelink', 'issuelinktype', 'label', 'nodeassociation',
            
            # 👥 USERS_GROUPS (8 tables)
            'cwd_user', 'cwd_group', 'cwd_membership', 'cwd_user_attributes',
            'userassociation', 'projectroleactor', 'app_user', 'cwd_directory',
            
            # 🔄 WORKFLOWS (5 tables)
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',
            'os_currentstep', 'os_wfentry',
            
            # ⚙️ CONFIGURATION (6 tables)
            'fieldconfiguration', 'fieldconfigscheme', 'permissionscheme',
            'schemepermissions', 'fieldscreen', 'fieldscreentab',
            
            # 📝 CHANGES_HISTORY (3 tables)
            'changegroup', 'changeitem', 'jiraaction',
            
            # 🔧 PLUGINS_MANAGEMENT (2 tables)
            'pluginversion', 'managedconfigurationitem',
            
            # 📊 LOOKUP_TABLES (6 tables)
            'issuestatus', 'priority', 'resolution', 'issuetype',
            'projectrole', 'projectcategory',
            
            # 🤖 SCRIPT_RUNNER (4 tables)
            'AO_4B00E6_SR_USER_PROP', 'AO_4B00E6_STASH_SETTINGS',
            'AO_4B00E6_UPGRADE_BACKUP', 'AO_786AC3_SQL_FAVOURITE',
            
            # 🎯 AGILE_BOARDS (3 tables)
            'AO_60DB71_RAPIDVIEW', 'AO_60DB71_SPRINT', 'AO_60DB71_ISSUERANKING',
            
            # 🔍 JSM_AUDIT (3 tables)
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]
        
        # Tables Phase 2 à traiter (39 tables)
        self.phase2_tables = [table for table in self.all_critical_tables 
                             if table not in self.phase1_tables]
        
        self.stats = {
            'start_time': datetime.now(),
            'tables_processed': 0,
            'tables_with_data': 0,
            'tables_empty': 0,
            'total_records': 0,
            'errors': []
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        try:
            conn = psycopg2.connect(**self.dw_config)
            conn.autocommit = False
            return conn
        except Exception as e:
            logger.error(f"❌ Erreur connexion DW: {e}")
            raise
    
    def connect_jira(self):
        """Connexion READ-ONLY à Jira"""
        try:
            conn = psycopg2.connect(**self.jira_config)
            conn.set_session(readonly=True, autocommit=True)
            return conn
        except Exception as e:
            logger.error(f"❌ Erreur connexion Jira: {e}")
            raise
    
    def get_common_columns(self, jira_cursor, dw_cursor, table_name):
        """Récupérer les colonnes communes entre Jira et staging"""
        try:
            # Colonnes source (Jira)
            jira_cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns
                WHERE table_schema = 'public' AND table_name = %s
                ORDER BY ordinal_position
            """, (table_name,))
            source_columns = [row[0] for row in jira_cursor.fetchall()]
            
            # Colonnes destination (staging) - exclure les colonnes ETL
            dw_cursor.execute("""
                SELECT column_name 
                FROM information_schema.columns
                WHERE table_schema = 'staging' AND table_name = %s
                AND column_name NOT IN ('instance_id', 'extracted_at')
                ORDER BY ordinal_position
            """, (table_name,))
            staging_columns = [row[0] for row in dw_cursor.fetchall()]
            
            # Colonnes communes
            common_columns = [col for col in source_columns if col in staging_columns]
            
            logger.info(f"   🔗 {table_name}: {len(common_columns)}/{len(source_columns)} colonnes communes")
            return common_columns
            
        except Exception as e:
            logger.error(f"❌ Erreur récupération colonnes pour {table_name}: {e}")
            return []
    
    def extract_table_data(self, table_name):
        """Extraire les données d'une table Jira vers staging"""
        jira_conn = self.connect_jira()
        dw_conn = self.connect_dw()
        
        try:
            jira_cursor = jira_conn.cursor()
            dw_cursor = dw_conn.cursor()
            
            logger.info(f"📥 Extraction: {table_name}")
            
            # Vérifier si la table existe dans Jira
            try:
                jira_cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
                source_count = jira_cursor.fetchone()[0]
            except Exception as e:
                logger.warning(f"   ⚠️ {table_name}: Table inaccessible - {str(e)[:50]}")
                self.stats['errors'].append(f"{table_name}: Table inaccessible")
                return 0
            
            if source_count == 0:
                logger.info(f"   📊 {table_name}: Table vide (0 records)")
                self.stats['tables_empty'] += 1
                return 0
            
            # Récupérer les colonnes communes
            common_columns = self.get_common_columns(jira_cursor, dw_cursor, table_name)
            
            if not common_columns:
                logger.error(f"   ❌ {table_name}: Aucune colonne commune")
                self.stats['errors'].append(f"{table_name}: Aucune colonne commune")
                return 0
            
            # Vider la table staging avant insertion
            dw_cursor.execute(f'DELETE FROM staging."{table_name}"')
            
            # Construire la requête d'extraction
            columns_list = ', '.join([f'"{col}"' for col in common_columns])
            placeholders = ', '.join(['%s'] * len(common_columns))
            
            extract_sql = f'SELECT {columns_list} FROM "{table_name}"'
            insert_sql = f'''
                INSERT INTO staging."{table_name}" 
                ({columns_list}, instance_id, extracted_at)
                VALUES ({placeholders}, %s, CURRENT_TIMESTAMP)
            '''
            
            # Extraction par batch de 5000 records
            batch_size = 5000
            total_inserted = 0
            
            jira_cursor.execute(extract_sql)
            
            while True:
                batch = jira_cursor.fetchmany(batch_size)
                if not batch:
                    break
                
                # Ajouter instance_id = 1 à chaque record
                batch_with_instance = [tuple(list(row) + [1]) for row in batch]
                
                # Insertion batch
                dw_cursor.executemany(insert_sql, batch_with_instance)
                total_inserted += len(batch)
                
                if total_inserted % 5000 == 0:
                    logger.info(f"   📊 {table_name}: {total_inserted:,} records insérés...")
            
            dw_conn.commit()
            
            # Vérification finale
            dw_cursor.execute(f'SELECT COUNT(*) FROM staging."{table_name}"')
            final_count = dw_cursor.fetchone()[0]
            
            logger.info(f"   ✅ {table_name}: {final_count:,} records extraits")
            
            if final_count > 0:
                self.stats['tables_with_data'] += 1
            
            return final_count
            
        except Exception as e:
            logger.error(f"   ❌ Erreur extraction {table_name}: {e}")
            dw_conn.rollback()
            self.stats['errors'].append(f"{table_name}: {e}")
            return 0
        finally:
            jira_conn.close()
            dw_conn.close()
    
    def extract_all_phase2_tables(self):
        """Extraire toutes les tables Phase 2"""
        logger.info(f"🚀 EXTRACTION PHASE 2 - {len(self.phase2_tables)} TABLES")
        logger.info("=" * 70)
        
        for i, table_name in enumerate(self.phase2_tables, 1):
            start_time = time.time()
            
            logger.info(f"📥 [{i:2d}/{len(self.phase2_tables)}] {table_name}")
            
            records = self.extract_table_data(table_name)
            
            duration = time.time() - start_time
            self.stats['tables_processed'] += 1
            self.stats['total_records'] += records
            
            logger.info(f"   ⏱️ {table_name}: {duration:.2f}s pour {records:,} records")
    
    def verify_extraction(self):
        """Vérifier l'extraction complète (52 tables)"""
        logger.info("\n🔍 VÉRIFICATION EXTRACTION COMPLÈTE")
        logger.info("=" * 60)
        
        dw_conn = self.connect_dw()
        cursor = dw_conn.cursor()
        
        try:
            total_staging_records = 0
            phase1_records = 0
            phase2_records = 0
            
            for table_name in self.all_critical_tables:
                cursor.execute(f'SELECT COUNT(*) FROM staging."{table_name}"')
                count = cursor.fetchone()[0]
                
                if table_name in self.phase1_tables:
                    phase1_records += count
                    logger.info(f"   ✅ staging.{table_name}: {count:,} records (Phase 1)")
                else:
                    phase2_records += count
                    status = "✅" if count > 0 else "📊"
                    logger.info(f"   {status} staging.{table_name}: {count:,} records (Phase 2)")
                
                total_staging_records += count
            
            logger.info(f"\n📊 RÉSUMÉ EXTRACTION:")
            logger.info(f"   Phase 1: {phase1_records:,} records")
            logger.info(f"   Phase 2: {phase2_records:,} records")
            logger.info(f"   TOTAL: {total_staging_records:,} records")
            
            return total_staging_records
            
        finally:
            dw_conn.close()

def main():
    """Point d'entrée principal"""
    print("🚀 EXTRACTION PHASE 2 - 39 TABLES RESTANTES")
    print("=" * 60)
    print("📋 Extraction données vers staging.* (complet)")
    print("🎯 Source: jiradb (READ-ONLY)")
    print("🎯 Destination: aya.staging.*")
    print("=" * 60)
    
    extractor = Phase2Extractor()
    
    try:
        # Extraire toutes les tables Phase 2
        extractor.extract_all_phase2_tables()
        
        # Vérifier l'extraction complète
        total_records = extractor.verify_extraction()
        
        # Statistiques finales
        duration = (datetime.now() - extractor.stats['start_time']).total_seconds()
        
        logger.info(f"\n🎉 EXTRACTION PHASE 2 TERMINÉE!")
        logger.info(f"⏱️ Durée: {duration:.2f} secondes")
        logger.info(f"📊 Tables traitées: {extractor.stats['tables_processed']}/{len(extractor.phase2_tables)}")
        logger.info(f"📈 Tables avec données: {extractor.stats['tables_with_data']}")
        logger.info(f"📊 Tables vides: {extractor.stats['tables_empty']}")
        logger.info(f"📈 Records Phase 2: {extractor.stats['total_records']:,}")
        logger.info(f"❌ Erreurs: {len(extractor.stats['errors'])}")
        
        if extractor.stats['errors']:
            logger.warning(f"🚨 Erreurs rencontrées:")
            for error in extractor.stats['errors'][:5]:
                logger.warning(f"   - {error}")
        
        logger.info(f"\n🎯 STAGING COMPLET!")
        logger.info(f"   52/52 tables avec schémas + données")
        logger.info(f"   Total records: {total_records:,}")
        logger.info(f"\n🎯 PROCHAINE ÉTAPE:")
        logger.info(f"   Exécuter: python build_transform_schema.py")
        logger.info(f"   Pour créer le schéma TRANSFORM")
        
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
