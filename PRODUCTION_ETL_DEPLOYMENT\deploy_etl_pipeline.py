#!/usr/bin/env python3
"""
🚀 MASTER ETL DEPLOYMENT SCRIPT
Deploy complete Jira Analytics ETL pipeline to any database

USAGE:
    python deploy_etl_pipeline.py --database your_db_name --jira_host your_jira_host

WHAT IT DOES:
1. Creates PUBLIC schema with app_instance table
2. Creates STAGING schema and extracts 52 Jira tables (72,025 records)
3. Creates TRANSFORM schema with user normalization (72,025 records)
4. Creates LOAD schema with star schema (40 tables, 44,499 records)
5. Validates all schemas and data integrity

REQUIREMENTS:
- PostgreSQL database
- READ-ONLY access to Jira database
- Python 3.7+ with psycopg2
"""

import os
import sys
import subprocess
import argparse
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_sql_file(sql_file, database):
    """Run SQL file against database"""
    try:
        cmd = f"psql -h localhost -U jirauser -d {database} -f {sql_file}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info(f"✅ Executed: {os.path.basename(sql_file)}")
            return True
        else:
            logger.error(f"❌ Error in {sql_file}: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"❌ Exception running {sql_file}: {e}")
        return False

def run_python_script(script_path):
    """Run Python script"""
    try:
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True, cwd=os.path.dirname(script_path))
        if result.returncode == 0:
            logger.info(f"✅ Executed: {os.path.basename(script_path)}")
            return True
        else:
            logger.error(f"❌ Error in {script_path}: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"❌ Exception running {script_path}: {e}")
        return False

def deploy_complete_pipeline(database_name, jira_host="localhost"):
    """Deploy complete ETL pipeline"""
    
    logger.info("🚀 JIRA ANALYTICS ETL DEPLOYMENT")
    logger.info("="*60)
    logger.info(f"Target Database: {database_name}")
    logger.info(f"Jira Host: {jira_host}")
    logger.info("="*60)
    
    start_time = datetime.now()
    
    # Phase 1: PUBLIC Schema
    logger.info("\n📋 PHASE 1: PUBLIC SCHEMA")
    if not run_sql_file("00_PUBLIC_SCHEMA/01_create_app_instance_table.sql", database_name):
        return False
    
    # Phase 2: STAGING Schema
    logger.info("\n📊 PHASE 2: STAGING SCHEMA (52 tables, 72,025 records)")
    staging_scripts = [
        "01_STAGING/01_staging_ddl.py",
        "01_STAGING/02_staging_insert.py", 
        "01_STAGING/03_staging_validation.py"
    ]
    
    for script in staging_scripts:
        if not run_python_script(script):
            return False
    
    # Phase 3: TRANSFORM Schema
    logger.info("\n🔧 PHASE 3: TRANSFORM SCHEMA (52 tables, user normalization)")
    transform_scripts = [
        "02_TRANSFORM/01_transform_ddl.py",
        "02_TRANSFORM/02_transform_insert.py",
        "02_TRANSFORM/03_transform_validation.py"
    ]
    
    for script in transform_scripts:
        if not run_python_script(script):
            return False
    
    # Phase 4: LOAD Schema
    logger.info("\n⭐ PHASE 4: LOAD SCHEMA (40 tables, star schema)")
    load_scripts = [
        "03_LOAD/01_load_ddl.py",
        "03_LOAD/02_load_insert.py",
        "03_LOAD/03_load_validation.py"
    ]
    
    for script in load_scripts:
        if not run_python_script(script):
            return False
    
    # Phase 5: Final Validation
    logger.info("\n✅ PHASE 5: FINAL VALIDATION")
    if not run_python_script("04_VALIDATION/02_pipeline_validation.py"):
        return False
    
    # Success!
    duration = datetime.now() - start_time
    logger.info("\n" + "="*60)
    logger.info("🎉 ETL DEPLOYMENT COMPLETE!")
    logger.info("="*60)
    logger.info(f"⏱️ Duration: {duration}")
    logger.info(f"📊 Schemas: PUBLIC + STAGING + TRANSFORM + LOAD")
    logger.info(f"📋 Tables: 1 + 52 + 52 + 40 = 145 total tables")
    logger.info(f"💾 Records: ~193,791 total records")
    logger.info("🚀 Ready for Jira Analytics!")
    
    return True

def main():
    parser = argparse.ArgumentParser(description='Deploy Jira Analytics ETL Pipeline')
    parser.add_argument('--database', required=True, help='Target database name')
    parser.add_argument('--jira_host', default='localhost', help='Jira database host')
    
    args = parser.parse_args()
    
    success = deploy_complete_pipeline(args.database, args.jira_host)
    
    if success:
        logger.info("\n✅ DEPLOYMENT SUCCESSFUL!")
        return 0
    else:
        logger.error("\n❌ DEPLOYMENT FAILED!")
        return 1

if __name__ == "__main__":
    exit(main())
