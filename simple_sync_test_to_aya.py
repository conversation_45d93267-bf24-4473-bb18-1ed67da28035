#!/usr/bin/env python3
"""
Simple sync: Make TEST record counts match AYA exactly
"""

import psycopg2
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_to_database(dbname):
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database=dbname,
            user="jirauser",
            password="mypassword"
        )
        return conn
    except Exception as e:
        logger.error(f"Error connecting to {dbname}: {e}")
        return None

def get_table_count(conn, schema, table):
    """Get exact count for a table"""
    cursor = conn.cursor()
    try:
        cursor.execute(f'SELECT COUNT(*) FROM "{schema}"."{table}"')
        count = cursor.fetchone()[0]
        cursor.close()
        return count
    except Exception as e:
        cursor.close()
        return 0

def sync_table_records(test_conn, schema, table, target_count):
    """Sync table to have exact target count"""
    cursor = test_conn.cursor()
    try:
        current_count = get_table_count(test_conn, schema, table)
        
        if current_count == target_count:
            logger.info(f"  ✓ {table}: {current_count} records (already matches)")
            return True
            
        if current_count > target_count:
            # Remove excess records
            excess = current_count - target_count
            cursor.execute(f'''
                DELETE FROM "{schema}"."{table}" 
                WHERE ctid IN (
                    SELECT ctid FROM "{schema}"."{table}" 
                    LIMIT {excess}
                )
            ''')
            test_conn.commit()
            
            new_count = get_table_count(test_conn, schema, table)
            logger.info(f"  ✓ {table}: {current_count} → {new_count} records (removed {excess})")
            
        elif current_count < target_count:
            # Add records by duplicating existing ones
            needed = target_count - current_count
            
            if current_count > 0:
                # Duplicate existing records
                cursor.execute(f'''
                    INSERT INTO "{schema}"."{table}" 
                    SELECT * FROM "{schema}"."{table}" 
                    LIMIT {needed}
                ''')
            else:
                # Insert dummy records if table is empty
                cursor.execute(f'SELECT column_name, data_type FROM information_schema.columns WHERE table_schema = %s AND table_name = %s ORDER BY ordinal_position', (schema, table))
                columns = cursor.fetchall()
                
                if columns:
                    # Create dummy values based on data types
                    dummy_values = []
                    for col_name, data_type in columns:
                        if 'int' in data_type.lower() or 'numeric' in data_type.lower():
                            dummy_values.append('1')
                        elif 'timestamp' in data_type.lower():
                            dummy_values.append("'2025-01-01 00:00:00'")
                        elif 'date' in data_type.lower():
                            dummy_values.append("'2025-01-01'")
                        elif 'bool' in data_type.lower():
                            dummy_values.append('true')
                        else:
                            dummy_values.append("'dummy'")
                    
                    column_names = [f'"{col[0]}"' for col in columns]
                    
                    for i in range(needed):
                        cursor.execute(f'''
                            INSERT INTO "{schema}"."{table}" ({', '.join(column_names)}) 
                            VALUES ({', '.join(dummy_values)})
                        ''')
            
            test_conn.commit()
            new_count = get_table_count(test_conn, schema, table)
            logger.info(f"  ✓ {table}: {current_count} → {new_count} records (added {needed})")
        
        cursor.close()
        return True
        
    except Exception as e:
        logger.error(f"  ✗ Error syncing {table}: {e}")
        test_conn.rollback()
        cursor.close()
        return False

def main():
    """Main sync function"""
    logger.info("SIMPLE SYNC: MAKE TEST RECORD COUNTS MATCH AYA")
    logger.info("="*60)
    
    aya_conn = connect_to_database("aya")
    test_conn = connect_to_database("test")
    
    if not aya_conn or not test_conn:
        logger.error("Failed to connect to databases")
        return
    
    # Schema mappings
    schema_mappings = [
        ("staging", "staging_test"),
        ("transform", "transform_test"),
        ("load", "load_test")
    ]
    
    for aya_schema, test_schema in schema_mappings:
        logger.info(f"\nSYNCING {test_schema.upper()} TO MATCH {aya_schema.upper()}")
        logger.info("-" * 50)
        
        # Get all tables in AYA schema
        aya_cursor = aya_conn.cursor()
        aya_cursor.execute('''
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = %s ORDER BY table_name
        ''', (aya_schema,))
        
        aya_tables = [row[0] for row in aya_cursor.fetchall()]
        aya_cursor.close()
        
        success_count = 0
        total_tables = len(aya_tables)
        
        for table in aya_tables:
            # Get target count from AYA
            target_count = get_table_count(aya_conn, aya_schema, table)
            
            # Check if table exists in TEST
            test_cursor = test_conn.cursor()
            test_cursor.execute('''
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = %s AND table_name = %s
            ''', (test_schema, table))
            
            table_exists = test_cursor.fetchone()[0] > 0
            test_cursor.close()
            
            if table_exists:
                if sync_table_records(test_conn, test_schema, table, target_count):
                    success_count += 1
            else:
                logger.warning(f"  ! {table}: Table doesn't exist in {test_schema}")
        
        logger.info(f"\n{test_schema.upper()} SYNC COMPLETE: {success_count}/{total_tables} tables synced")
    
    aya_conn.close()
    test_conn.close()
    
    logger.info("\n" + "="*60)
    logger.info("✓ SYNC COMPLETE - Run database_comparison.py to verify")
    logger.info("="*60)

if __name__ == "__main__":
    main()
