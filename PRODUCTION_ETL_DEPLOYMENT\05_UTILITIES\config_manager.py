#!/usr/bin/env python3
"""
🔧 ETL CONFIGURATION MANAGER
Manage database connections and ETL configuration for Jira Analytics

FEATURES:
- Database connection management
- Jira instance configuration
- ETL pipeline settings
- Environment-specific configurations
"""

import os
import json
import psycopg2
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ETLConfigManager:
    """Manage ETL configuration and database connections"""
    
    def __init__(self, config_file="etl_config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self):
        """Load configuration from file or create default"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r') as f:
                return json.load(f)
        else:
            return self.create_default_config()
    
    def create_default_config(self):
        """Create default configuration"""
        default_config = {
            "database": {
                "host": "localhost",
                "port": 5432,
                "user": "jirauser",
                "password": "mypassword",
                "target_db": "analytics_db"
            },
            "jira_instance": {
                "host": "localhost",
                "port": 5432,
                "database": "jiradb",
                "user": "jirauser",
                "password": "mypassword",
                "client_name": "default_client"
            },
            "etl_settings": {
                "batch_size": 5000,
                "timeout_seconds": 300,
                "max_retries": 3,
                "validate_data": True,
                "create_indexes": True
            },
            "schemas": {
                "staging": "staging",
                "transform": "transform", 
                "load": "load"
            }
        }
        
        self.save_config(default_config)
        logger.info(f"✅ Created default config: {self.config_file}")
        return default_config
    
    def save_config(self, config=None):
        """Save configuration to file"""
        if config is None:
            config = self.config
            
        with open(self.config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"💾 Saved config: {self.config_file}")
    
    def get_target_db_connection(self):
        """Get connection to target analytics database"""
        db_config = self.config["database"]
        
        try:
            conn = psycopg2.connect(
                host=db_config["host"],
                port=db_config["port"],
                database=db_config["target_db"],
                user=db_config["user"],
                password=db_config["password"]
            )
            logger.info(f"✅ Connected to target DB: {db_config['target_db']}")
            return conn
        except Exception as e:
            logger.error(f"❌ Failed to connect to target DB: {e}")
            return None
    
    def get_jira_db_connection(self):
        """Get READ-ONLY connection to Jira database"""
        jira_config = self.config["jira_instance"]
        
        try:
            conn = psycopg2.connect(
                host=jira_config["host"],
                port=jira_config["port"],
                database=jira_config["database"],
                user=jira_config["user"],
                password=jira_config["password"]
            )
            
            # Set to READ-ONLY for safety
            conn.set_session(readonly=True)
            logger.info(f"✅ Connected to Jira DB: {jira_config['database']} (READ-ONLY)")
            return conn
        except Exception as e:
            logger.error(f"❌ Failed to connect to Jira DB: {e}")
            return None
    
    def test_connections(self):
        """Test all database connections"""
        logger.info("🔍 TESTING DATABASE CONNECTIONS")
        logger.info("="*50)
        
        # Test target database
        target_conn = self.get_target_db_connection()
        target_ok = target_conn is not None
        if target_conn:
            target_conn.close()
        
        # Test Jira database
        jira_conn = self.get_jira_db_connection()
        jira_ok = jira_conn is not None
        if jira_conn:
            jira_conn.close()
        
        # Results
        logger.info(f"Target DB: {'✅ OK' if target_ok else '❌ FAILED'}")
        logger.info(f"Jira DB: {'✅ OK' if jira_ok else '❌ FAILED'}")
        
        return target_ok and jira_ok
    
    def setup_app_instance(self):
        """Setup app_instance table with current configuration"""
        conn = self.get_target_db_connection()
        if not conn:
            return False
        
        try:
            cursor = conn.cursor()
            jira_config = self.config["jira_instance"]
            
            # Insert/update app_instance
            cursor.execute("""
                INSERT INTO public.app_instance (
                    instance_id, host, port, db_name, db_user, db_password, client_name, last_sync
                ) VALUES (
                    1, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP
                ) ON CONFLICT (instance_id) DO UPDATE SET
                    host = EXCLUDED.host,
                    port = EXCLUDED.port,
                    db_name = EXCLUDED.db_name,
                    db_user = EXCLUDED.db_user,
                    db_password = EXCLUDED.db_password,
                    client_name = EXCLUDED.client_name,
                    last_sync = CURRENT_TIMESTAMP
            """, (
                jira_config["host"],
                jira_config["port"],
                jira_config["database"],
                jira_config["user"],
                jira_config["password"],
                jira_config["client_name"]
            ))
            
            conn.commit()
            cursor.close()
            conn.close()
            
            logger.info("✅ App instance configured successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup app instance: {e}")
            conn.rollback()
            conn.close()
            return False
    
    def update_jira_config(self, host, port, database, user, password, client_name):
        """Update Jira instance configuration"""
        self.config["jira_instance"] = {
            "host": host,
            "port": port,
            "database": database,
            "user": user,
            "password": password,
            "client_name": client_name
        }
        
        self.save_config()
        logger.info("✅ Jira configuration updated")
    
    def update_target_db_config(self, host, port, database, user, password):
        """Update target database configuration"""
        self.config["database"] = {
            "host": host,
            "port": port,
            "target_db": database,
            "user": user,
            "password": password
        }
        
        self.save_config()
        logger.info("✅ Target database configuration updated")
    
    def get_etl_settings(self):
        """Get ETL pipeline settings"""
        return self.config["etl_settings"]
    
    def get_schema_names(self):
        """Get schema names configuration"""
        return self.config["schemas"]

def main():
    """Interactive configuration setup"""
    print("🔧 ETL CONFIGURATION MANAGER")
    print("="*50)
    
    config_manager = ETLConfigManager()
    
    # Test connections
    if config_manager.test_connections():
        print("✅ All database connections working!")
        
        # Setup app instance
        if config_manager.setup_app_instance():
            print("✅ App instance configured!")
        
        print("\n🎯 Configuration ready for ETL deployment!")
    else:
        print("❌ Connection issues detected. Please check configuration.")
        print(f"📝 Edit: {config_manager.config_file}")

if __name__ == "__main__":
    main()
