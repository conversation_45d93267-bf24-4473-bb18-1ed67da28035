#!/usr/bin/env python3
"""
🌟 CREATE MISSING DIMENSION TABLES
Create missing dimension tables needed for proper star schema FK relationships

CREATES:
1. dim_priorities - Issue priorities
2. dim_statuses - Issue statuses  
3. dim_resolutions - Issue resolutions
4. dim_issue_types - Issue types
5. dim_reporters - Issue reporters (reference to dim_users)
6. dim_assignees - Issue assignees (reference to dim_users)
7. dim_creators - Issue creators (reference to dim_users)
"""

import psycopg2
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_to_database(dbname="aya"):
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database=dbname,
            user="jirauser",
            password="mypassword"
        )
        return conn
    except Exception as e:
        logger.error(f"Error connecting to {dbname}: {e}")
        return None

def create_dim_priorities():
    """Create dim_priorities table"""
    logger.info("🔧 CREATING dim_priorities")
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        # Create dimension table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS load.dim_priorities (
                id SERIAL PRIMARY KEY,
                priority_name VARCHAR(100) UNIQUE NOT NULL,
                priority_description VARCHAR(500),
                priority_sequence INTEGER,
                priority_color VARCHAR(20),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Insert data from fact_issues
        cursor.execute('''
            INSERT INTO load.dim_priorities (priority_name, priority_description, priority_sequence)
            SELECT DISTINCT 
                COALESCE(priority, 'Unknown') as priority_name,
                'Priority: ' || COALESCE(priority, 'Unknown') as priority_description,
                CASE COALESCE(priority, 'Unknown')
                    WHEN 'Highest' THEN 1
                    WHEN 'High' THEN 2
                    WHEN 'Medium' THEN 3
                    WHEN 'Low' THEN 4
                    WHEN 'Lowest' THEN 5
                    ELSE 99
                END as priority_sequence
            FROM load.fact_issues
            WHERE priority IS NOT NULL
            ON CONFLICT (priority_name) DO NOTHING
        ''')
        
        # Add default priority if none exist
        cursor.execute('''
            INSERT INTO load.dim_priorities (priority_name, priority_description, priority_sequence)
            SELECT 'Unknown', 'Unknown Priority', 99
            WHERE NOT EXISTS (SELECT 1 FROM load.dim_priorities)
        ''')
        
        conn.commit()
        
        # Get count
        cursor.execute('SELECT COUNT(*) FROM load.dim_priorities')
        count = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        logger.info(f"  ✅ Created dim_priorities with {count} records")
        return True
        
    except Exception as e:
        logger.error(f"  ❌ Error creating dim_priorities: {e}")
        conn.rollback()
        cursor.close()
        conn.close()
        return False

def create_dim_statuses():
    """Create dim_statuses table"""
    logger.info("🔧 CREATING dim_statuses")
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        # Create dimension table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS load.dim_statuses (
                id SERIAL PRIMARY KEY,
                status_name VARCHAR(100) UNIQUE NOT NULL,
                status_description VARCHAR(500),
                status_category VARCHAR(50),
                is_resolved BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Insert data from fact_issues
        cursor.execute('''
            INSERT INTO load.dim_statuses (status_name, status_description, status_category, is_resolved)
            SELECT DISTINCT 
                COALESCE(status, 'Unknown') as status_name,
                'Status: ' || COALESCE(status, 'Unknown') as status_description,
                CASE 
                    WHEN LOWER(COALESCE(status, '')) IN ('done', 'closed', 'resolved', 'complete') THEN 'Done'
                    WHEN LOWER(COALESCE(status, '')) IN ('in progress', 'in review', 'testing') THEN 'In Progress'
                    WHEN LOWER(COALESCE(status, '')) IN ('open', 'new', 'to do', 'backlog') THEN 'To Do'
                    ELSE 'Other'
                END as status_category,
                CASE 
                    WHEN LOWER(COALESCE(status, '')) IN ('done', 'closed', 'resolved', 'complete') THEN TRUE
                    ELSE FALSE
                END as is_resolved
            FROM load.fact_issues
            WHERE status IS NOT NULL
            ON CONFLICT (status_name) DO NOTHING
        ''')
        
        # Add default status if none exist
        cursor.execute('''
            INSERT INTO load.dim_statuses (status_name, status_description, status_category, is_resolved)
            SELECT 'Unknown', 'Unknown Status', 'Other', FALSE
            WHERE NOT EXISTS (SELECT 1 FROM load.dim_statuses)
        ''')
        
        conn.commit()
        
        # Get count
        cursor.execute('SELECT COUNT(*) FROM load.dim_statuses')
        count = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        logger.info(f"  ✅ Created dim_statuses with {count} records")
        return True
        
    except Exception as e:
        logger.error(f"  ❌ Error creating dim_statuses: {e}")
        conn.rollback()
        cursor.close()
        conn.close()
        return False

def create_dim_resolutions():
    """Create dim_resolutions table"""
    logger.info("🔧 CREATING dim_resolutions")
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        # Create dimension table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS load.dim_resolutions (
                id SERIAL PRIMARY KEY,
                resolution_name VARCHAR(100) UNIQUE NOT NULL,
                resolution_description VARCHAR(500),
                is_positive_resolution BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Insert data from fact_issues
        cursor.execute('''
            INSERT INTO load.dim_resolutions (resolution_name, resolution_description, is_positive_resolution)
            SELECT DISTINCT 
                COALESCE(resolution, 'Unresolved') as resolution_name,
                'Resolution: ' || COALESCE(resolution, 'Unresolved') as resolution_description,
                CASE 
                    WHEN LOWER(COALESCE(resolution, '')) IN ('fixed', 'done', 'complete', 'resolved') THEN TRUE
                    WHEN LOWER(COALESCE(resolution, '')) IN ('wont fix', 'duplicate', 'invalid', 'rejected') THEN FALSE
                    ELSE TRUE
                END as is_positive_resolution
            FROM load.fact_issues
            ON CONFLICT (resolution_name) DO NOTHING
        ''')
        
        conn.commit()
        
        # Get count
        cursor.execute('SELECT COUNT(*) FROM load.dim_resolutions')
        count = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        logger.info(f"  ✅ Created dim_resolutions with {count} records")
        return True
        
    except Exception as e:
        logger.error(f"  ❌ Error creating dim_resolutions: {e}")
        conn.rollback()
        cursor.close()
        conn.close()
        return False

def create_dim_issue_types():
    """Create dim_issue_types table"""
    logger.info("🔧 CREATING dim_issue_types")
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        # Create dimension table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS load.dim_issue_types (
                id SERIAL PRIMARY KEY,
                issue_type_name VARCHAR(100) UNIQUE NOT NULL,
                issue_type_description VARCHAR(500),
                issue_type_category VARCHAR(50),
                is_subtask BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Insert data from fact_issues
        cursor.execute('''
            INSERT INTO load.dim_issue_types (issue_type_name, issue_type_description, issue_type_category, is_subtask)
            SELECT DISTINCT 
                COALESCE(issue_type, 'Unknown') as issue_type_name,
                'Issue Type: ' || COALESCE(issue_type, 'Unknown') as issue_type_description,
                CASE 
                    WHEN LOWER(COALESCE(issue_type, '')) IN ('bug', 'defect', 'error') THEN 'Bug'
                    WHEN LOWER(COALESCE(issue_type, '')) IN ('story', 'feature', 'enhancement') THEN 'Feature'
                    WHEN LOWER(COALESCE(issue_type, '')) IN ('task', 'improvement') THEN 'Task'
                    WHEN LOWER(COALESCE(issue_type, '')) IN ('epic') THEN 'Epic'
                    WHEN LOWER(COALESCE(issue_type, '')) LIKE '%sub%' THEN 'Subtask'
                    ELSE 'Other'
                END as issue_type_category,
                CASE 
                    WHEN LOWER(COALESCE(issue_type, '')) LIKE '%sub%' THEN TRUE
                    ELSE FALSE
                END as is_subtask
            FROM load.fact_issues
            WHERE issue_type IS NOT NULL
            ON CONFLICT (issue_type_name) DO NOTHING
        ''')
        
        # Add default issue type if none exist
        cursor.execute('''
            INSERT INTO load.dim_issue_types (issue_type_name, issue_type_description, issue_type_category, is_subtask)
            SELECT 'Unknown', 'Unknown Issue Type', 'Other', FALSE
            WHERE NOT EXISTS (SELECT 1 FROM load.dim_issue_types)
        ''')
        
        conn.commit()
        
        # Get count
        cursor.execute('SELECT COUNT(*) FROM load.dim_issue_types')
        count = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        logger.info(f"  ✅ Created dim_issue_types with {count} records")
        return True
        
    except Exception as e:
        logger.error(f"  ❌ Error creating dim_issue_types: {e}")
        conn.rollback()
        cursor.close()
        conn.close()
        return False

def create_user_reference_dimensions():
    """Create user reference dimension tables"""
    logger.info("🔧 CREATING USER REFERENCE DIMENSIONS")
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        # Create views that reference dim_users for different roles
        
        # dim_reporters (view of dim_users)
        cursor.execute('''
            CREATE OR REPLACE VIEW load.dim_reporters AS
            SELECT 
                id,
                user_name as reporter_name,
                display_name as reporter_display_name,
                email_address as reporter_email,
                active as reporter_active,
                created_date as reporter_created_date
            FROM load.dim_users
        ''')
        
        # dim_assignees (view of dim_users)
        cursor.execute('''
            CREATE OR REPLACE VIEW load.dim_assignees AS
            SELECT 
                id,
                user_name as assignee_name,
                display_name as assignee_display_name,
                email_address as assignee_email,
                active as assignee_active,
                created_date as assignee_created_date
            FROM load.dim_users
        ''')
        
        # dim_creators (view of dim_users)
        cursor.execute('''
            CREATE OR REPLACE VIEW load.dim_creators AS
            SELECT 
                id,
                user_name as creator_name,
                display_name as creator_display_name,
                email_address as creator_email,
                active as creator_active,
                created_date as creator_created_date
            FROM load.dim_users
        ''')
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info(f"  ✅ Created user reference dimensions (views)")
        return True
        
    except Exception as e:
        logger.error(f"  ❌ Error creating user reference dimensions: {e}")
        conn.rollback()
        cursor.close()
        conn.close()
        return False

def validate_dimension_creation():
    """Validate that all dimension tables were created successfully"""
    logger.info("\n✅ VALIDATING DIMENSION CREATION")
    logger.info("="*60)
    
    conn = connect_to_database()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    # Check created tables
    expected_tables = [
        'dim_priorities',
        'dim_statuses', 
        'dim_resolutions',
        'dim_issue_types'
    ]
    
    expected_views = [
        'dim_reporters',
        'dim_assignees',
        'dim_creators'
    ]
    
    success = True
    
    # Check tables
    for table in expected_tables:
        cursor.execute('''
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'load' AND table_name = %s
        ''', (table,))
        
        exists = cursor.fetchone()[0] > 0
        
        if exists:
            cursor.execute(f'SELECT COUNT(*) FROM load."{table}"')
            count = cursor.fetchone()[0]
            logger.info(f"  ✅ {table}: {count} records")
        else:
            logger.error(f"  ❌ {table}: NOT FOUND")
            success = False
    
    # Check views
    for view in expected_views:
        cursor.execute('''
            SELECT COUNT(*) FROM information_schema.views 
            WHERE table_schema = 'load' AND table_name = %s
        ''', (view,))
        
        exists = cursor.fetchone()[0] > 0
        
        if exists:
            logger.info(f"  ✅ {view}: VIEW CREATED")
        else:
            logger.error(f"  ❌ {view}: VIEW NOT FOUND")
            success = False
    
    cursor.close()
    conn.close()
    
    return success

def main():
    """Main dimension creation function"""
    logger.info("🌟 CREATE MISSING DIMENSION TABLES")
    logger.info("="*70)
    logger.info("Creating dimension tables for proper star schema...")
    logger.info("="*70)
    
    start_time = datetime.now()
    
    # Create dimension tables
    steps = [
        ("dim_priorities", create_dim_priorities),
        ("dim_statuses", create_dim_statuses),
        ("dim_resolutions", create_dim_resolutions),
        ("dim_issue_types", create_dim_issue_types),
        ("user reference dimensions", create_user_reference_dimensions)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        logger.info(f"\n📋 Creating {step_name}...")
        if step_func():
            success_count += 1
        else:
            logger.error(f"❌ Failed to create {step_name}")
    
    # Validate creation
    validation_success = validate_dimension_creation()
    
    # Summary
    duration = datetime.now() - start_time
    logger.info("\n" + "="*70)
    logger.info("📊 DIMENSION CREATION SUMMARY")
    logger.info("="*70)
    logger.info(f"⏱️ Duration: {duration}")
    logger.info(f"🌟 Dimensions created: {success_count}/{len(steps)}")
    logger.info(f"✅ Validation: {'✅' if validation_success else '❌'}")
    
    overall_success = success_count == len(steps) and validation_success
    
    if overall_success:
        logger.info("\n🎉 ALL DIMENSION TABLES CREATED SUCCESSFULLY!")
        logger.info("🔗 Ready for foreign key creation!")
    else:
        logger.error("\n❌ Some dimension creation failed")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
