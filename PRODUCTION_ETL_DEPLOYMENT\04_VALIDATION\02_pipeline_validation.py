#!/usr/bin/env python3
"""
🔍 TEST PIPELINE VALIDATION
Validation complète du pipeline ETL test:
- staging_test → transform_test → load_test
- Comparaison avec les résultats aya (référence)

⚠️ ATTENTION: Script de validation READ-ONLY
"""

import psycopg2
import logging
from datetime import datetime

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'test_pipeline_validation_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TestPipelineValidator:
    """
    Validateur pour le pipeline ETL test complet
    """
    
    def __init__(self):
        # Configuration Data Warehouse TEST
        self.test_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'test',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # Configuration Data Warehouse REFERENCE (aya)
        self.aya_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # 52 tables critiques complètes
        self.all_critical_tables = [
            # 🔥 CORE_BUSINESS (12 tables)
            'jiraissue', 'project', 'component', 'projectversion',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelink', 'issuelinktype', 'label', 'nodeassociation',
            
            # 👥 USERS_GROUPS (8 tables)
            'cwd_user', 'cwd_group', 'cwd_membership', 'cwd_user_attributes',
            'userassociation', 'projectroleactor', 'app_user', 'cwd_directory',
            
            # 🔄 WORKFLOWS (5 tables)
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',
            'os_currentstep', 'os_wfentry',
            
            # ⚙️ CONFIGURATION (6 tables)
            'fieldconfiguration', 'fieldconfigscheme', 'permissionscheme',
            'schemepermissions', 'fieldscreen', 'fieldscreentab',
            
            # 📝 CHANGES_HISTORY (3 tables)
            'changegroup', 'changeitem', 'jiraaction',
            
            # 🔧 PLUGINS_MANAGEMENT (2 tables)
            'pluginversion', 'managedconfigurationitem',
            
            # 📊 LOOKUP_TABLES (6 tables)
            'issuestatus', 'priority', 'resolution', 'issuetype',
            'projectrole', 'projectcategory',
            
            # 🤖 SCRIPT_RUNNER (4 tables)
            'AO_4B00E6_SR_USER_PROP', 'AO_4B00E6_STASH_SETTINGS',
            'AO_4B00E6_UPGRADE_BACKUP', 'AO_786AC3_SQL_FAVOURITE',
            
            # 🎯 AGILE_BOARDS (3 tables)
            'AO_60DB71_RAPIDVIEW', 'AO_60DB71_SPRINT', 'AO_60DB71_ISSUERANKING',
            
            # 🔍 JSM_AUDIT (3 tables)
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]
        
        self.validation_results = {
            'start_time': datetime.now(),
            'staging_comparison': {},
            'transform_comparison': {},
            'schema_validation': {},
            'errors': []
        }
    
    def connect_test(self):
        """Connexion au Data Warehouse TEST"""
        try:
            conn = psycopg2.connect(**self.test_config)
            conn.set_session(readonly=True, autocommit=True)
            return conn
        except Exception as e:
            logger.error(f"❌ Erreur connexion TEST: {e}")
            raise
    
    def connect_aya(self):
        """Connexion au Data Warehouse AYA (référence)"""
        try:
            conn = psycopg2.connect(**self.aya_config)
            conn.set_session(readonly=True, autocommit=True)
            return conn
        except Exception as e:
            logger.error(f"❌ Erreur connexion AYA: {e}")
            raise
    
    def validate_staging_schemas(self):
        """Valider les schémas staging_test vs staging"""
        logger.info("🔍 VALIDATION SCHÉMAS STAGING")
        logger.info("=" * 50)
        
        test_conn = self.connect_test()
        aya_conn = self.connect_aya()
        
        try:
            test_cursor = test_conn.cursor()
            aya_cursor = aya_conn.cursor()
            
            staging_test_total = 0
            staging_aya_total = 0
            
            for table_name in self.all_critical_tables:
                # Compter records staging_test
                try:
                    test_cursor.execute(f'SELECT COUNT(*) FROM staging_test."{table_name}"')
                    test_count = test_cursor.fetchone()[0]
                except:
                    test_count = 0
                
                # Compter records staging (aya)
                try:
                    aya_cursor.execute(f'SELECT COUNT(*) FROM staging."{table_name}"')
                    aya_count = aya_cursor.fetchone()[0]
                except:
                    aya_count = 0
                
                staging_test_total += test_count
                staging_aya_total += aya_count
                
                # Comparaison
                if test_count == aya_count:
                    status = "✅"
                elif test_count == 0:
                    status = "❌"
                else:
                    status = "⚠️"
                
                logger.info(f"   {status} {table_name}: test={test_count:,} vs aya={aya_count:,}")
                
                self.validation_results['staging_comparison'][table_name] = {
                    'test_count': test_count,
                    'aya_count': aya_count,
                    'match': test_count == aya_count
                }
            
            logger.info(f"\n📊 RÉSUMÉ STAGING:")
            logger.info(f"   staging_test: {staging_test_total:,} records")
            logger.info(f"   staging (aya): {staging_aya_total:,} records")
            logger.info(f"   Match: {'✅' if staging_test_total == staging_aya_total else '❌'}")
            
            return staging_test_total, staging_aya_total
            
        finally:
            test_conn.close()
            aya_conn.close()
    
    def validate_transform_schemas(self):
        """Valider les schémas transform_test vs transform"""
        logger.info("\n🔍 VALIDATION SCHÉMAS TRANSFORM")
        logger.info("=" * 50)
        
        test_conn = self.connect_test()
        aya_conn = self.connect_aya()
        
        try:
            test_cursor = test_conn.cursor()
            aya_cursor = aya_conn.cursor()
            
            # Lister les tables transform_test
            test_cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'transform_test'
                ORDER BY table_name
            """)
            test_tables = [row[0] for row in test_cursor.fetchall()]
            
            # Lister les tables transform (aya)
            aya_cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'transform'
                ORDER BY table_name
            """)
            aya_tables = [row[0] for row in aya_cursor.fetchall()]
            
            logger.info(f"📊 Tables transform_test: {len(test_tables)}")
            logger.info(f"📊 Tables transform (aya): {len(aya_tables)}")
            
            # Comparer les structures
            common_tables = set(test_tables) & set(aya_tables)
            logger.info(f"📊 Tables communes: {len(common_tables)}")
            
            for table_name in sorted(common_tables):
                # Compter colonnes transform_test
                test_cursor.execute("""
                    SELECT COUNT(*) FROM information_schema.columns 
                    WHERE table_schema = 'transform_test' AND table_name = %s
                """, (table_name,))
                test_cols = test_cursor.fetchone()[0]
                
                # Compter colonnes transform (aya)
                aya_cursor.execute("""
                    SELECT COUNT(*) FROM information_schema.columns 
                    WHERE table_schema = 'transform' AND table_name = %s
                """, (table_name,))
                aya_cols = aya_cursor.fetchone()[0]
                
                status = "✅" if test_cols == aya_cols else "⚠️"
                logger.info(f"   {status} {table_name}: test={test_cols} cols vs aya={aya_cols} cols")
            
            return len(test_tables), len(aya_tables)
            
        finally:
            test_conn.close()
            aya_conn.close()
    
    def generate_validation_report(self):
        """Générer le rapport de validation final"""
        logger.info("\n📋 RAPPORT DE VALIDATION FINAL")
        logger.info("=" * 60)
        
        # Statistiques staging
        staging_matches = sum(1 for result in self.validation_results['staging_comparison'].values() 
                            if result['match'])
        staging_total = len(self.validation_results['staging_comparison'])
        
        logger.info(f"🎯 STAGING: {staging_matches}/{staging_total} tables correspondent")
        
        # Durée totale
        duration = (datetime.now() - self.validation_results['start_time']).total_seconds()
        logger.info(f"⏱️ Durée validation: {duration:.2f} secondes")
        
        # Erreurs
        if self.validation_results['errors']:
            logger.warning(f"❌ Erreurs: {len(self.validation_results['errors'])}")
            for error in self.validation_results['errors'][:3]:
                logger.warning(f"   - {error}")
        
        # Conclusion
        if staging_matches >= 45:  # Au moins 45/52 tables
            logger.info(f"\n🎉 VALIDATION RÉUSSIE!")
            logger.info(f"   Pipeline ETL test fonctionnel")
            logger.info(f"   Prêt pour les tests de transformation")
        else:
            logger.warning(f"\n⚠️ VALIDATION PARTIELLE")
            logger.warning(f"   Seulement {staging_matches}/52 tables correspondent")

def main():
    """Point d'entrée principal"""
    print("🔍 VALIDATION PIPELINE ETL TEST")
    print("=" * 60)
    print("📋 Comparaison test vs aya (référence)")
    print("🎯 Validation: staging_test, transform_test")
    print("=" * 60)
    
    validator = TestPipelineValidator()
    
    try:
        # Valider les schémas staging
        staging_test_total, staging_aya_total = validator.validate_staging_schemas()
        
        # Valider les schémas transform
        transform_test_total, transform_aya_total = validator.validate_transform_schemas()
        
        # Générer le rapport final
        validator.generate_validation_report()
        
        logger.info(f"\n🎯 VALIDATION TERMINÉE!")
        logger.info(f"   staging_test: {staging_test_total:,} records")
        logger.info(f"   transform_test: {transform_test_total} tables")
        
    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
