#!/usr/bin/env python3
"""
✅ VALIDATION COMPLÈTE DES CORRECTIONS
Vérifier que TOUS les problèmes identifiés ont été corrigés

VALIDATION:
1. ✅ Date conversion: Vérifier epoch → timestamp correct
2. ✅ Numeric columns: Vérifier inclusion des colonnes numériques  
3. ✅ NULL handling: Vérifier gestion NULL appropriée
4. ✅ User normalization: Vérifier JOIN avec cwd_user
5. ✅ Column dropping: Vérifier suppression colonnes inutiles
6. ✅ Text cleaning: Vérifier TRIM appliqué
7. ✅ Data quality: Vérifier scoring réel
8. ✅ Cross-validation: Comparer STAGING vs TRANSFORM
"""

import psycopg2
import logging
from datetime import datetime

# Configuration logging simple
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'validation_complete_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ValidateCompleteFix:
    """
    Validateur complet des corrections appliquées
    """
    
    def __init__(self):
        # Configuration connexions
        self.jira_config = {
            'host': 'localhost', 'port': 5432, 'database': 'jiradb',
            'user': 'jirauser', 'password': 'mypassword'
        }
        
        self.dwh_config = {
            'host': 'localhost', 'port': 5432, 'database': 'aya',
            'user': 'jirauser', 'password': 'mypassword'
        }
        
        # Tables critiques avec problèmes corrigés
        self.critical_tables_52 = [
            'jiraissue', 'project', 'component', 'projectversion',
            'customfield', 'customfieldvalue', 'worklog', 'fileattachment',
            'issuelink', 'issuelinktype', 'label', 'nodeassociation',
            'cwd_user', 'cwd_group', 'cwd_membership', 'cwd_user_attributes',
            'userassociation', 'projectroleactor', 'app_user', 'cwd_directory',
            'jiraworkflows', 'workflowscheme', 'workflowschemeentity',
            'os_currentstep', 'os_wfentry',
            'fieldconfiguration', 'fieldconfigscheme', 'permissionscheme',
            'schemepermissions', 'fieldscreen', 'fieldscreentab',
            'changegroup', 'changeitem', 'jiraaction',
            'pluginversion', 'managedconfigurationitem',
            'issuestatus', 'priority', 'resolution', 'issuetype',
            'projectrole', 'projectcategory',
            'AO_4B00E6_SR_USER_PROP', 'AO_4B00E6_STASH_SETTINGS',
            'AO_4B00E6_UPGRADE_BACKUP', 'AO_786AC3_SQL_FAVOURITE',
            'AO_60DB71_RAPIDVIEW', 'AO_60DB71_SPRINT', 'AO_60DB71_ISSUERANKING',
            'AO_C77861_AUDIT_ENTITY', 'AO_C77861_AUDIT_ACTION_CACHE',
            'AO_C77861_AUDIT_CATEGORY_CACHE'
        ]
        
        # Colonnes de dates corrigées
        self.date_columns_fixed = {
            'jiraissue': ['created', 'updated', 'duedate', 'resolutiondate'],
            'changegroup': ['created'],
            'worklog': ['created', 'updated', 'startdate'],
        }
        
        # Colonnes numériques corrigées
        self.numeric_columns_fixed = {
            'worklog': ['rolelevel'],
            'component': ['assigneetype'], 
            'project': ['pcounter']
        }
        
        # Colonnes utilisateur normalisées
        self.user_columns_normalized = {
            'jiraissue': ['assignee', 'reporter', 'creator'],
            'worklog': ['author', 'updateauthor'],
            'changegroup': ['author'],
            'component': ['lead'],
            'project': ['lead']
        }
        
        # Résultats validation
        self.validation_results = {
            'tables_validated': 0,
            'date_fixes_validated': 0,
            'numeric_fixes_validated': 0,
            'user_normalizations_validated': 0,
            'data_quality_validated': 0,
            'issues_found': [],
            'success_rate': 0
        }
    
    def connect_jira_readonly(self):
        """Connexion READ-ONLY sécurisée à Jira"""
        conn = psycopg2.connect(**self.jira_config)
        conn.set_session(readonly=True, autocommit=True)
        return conn
    
    def connect_dwh(self):
        """Connexion READ-ONLY au Data Warehouse pour validation"""
        conn = psycopg2.connect(**self.dwh_config)
        conn.set_session(readonly=True, autocommit=True)
        return conn
    
    def validate_date_conversion_fix(self, table_name):
        """
        VALIDATION 1: Vérifier que les dates epoch ont été corrigées
        """
        if table_name not in self.date_columns_fixed:
            return True
        
        logger.info(f"  VALIDATION 1: Conversion dates pour {table_name}")
        
        try:
            # Connexions
            jira_conn = self.connect_jira_readonly()
            jira_cursor = jira_conn.cursor()
            
            dwh_conn = self.connect_dwh()
            dwh_cursor = dwh_conn.cursor()
            
            for col in self.date_columns_fixed[table_name]:
                try:
                    # Échantillon JIRADB (référence correcte)
                    jira_cursor.execute(f'SELECT "{col}" FROM "{table_name}" WHERE "{col}" IS NOT NULL LIMIT 1')
                    jiradb_sample = jira_cursor.fetchone()
                    
                    # Échantillon TRANSFORM (doit être corrigé)
                    dwh_cursor.execute(f'SELECT "{col}" FROM transform."{table_name}_clean" WHERE "{col}" IS NOT NULL LIMIT 1')
                    transform_sample = dwh_cursor.fetchone()
                    
                    if jiradb_sample and transform_sample:
                        jiradb_date = jiradb_sample[0]
                        transform_date = transform_sample[0]
                        
                        # Vérifier que transform n'a plus de dates 1970
                        if str(transform_date).startswith('1970'):
                            logger.error(f"    ECHEC: {col} encore en epoch (1970)")
                            self.validation_results['issues_found'].append(f"{table_name}.{col}: Still epoch dates")
                            return False
                        else:
                            logger.info(f"    SUCCES: {col} converti correctement")
                            logger.info(f"      JIRADB: {jiradb_date}")
                            logger.info(f"      TRANSFORM: {transform_date}")
                            self.validation_results['date_fixes_validated'] += 1
                    
                except Exception as e:
                    logger.warning(f"    ERREUR validation {col}: {e}")
            
            jira_conn.close()
            dwh_conn.close()
            return True
            
        except Exception as e:
            logger.error(f"  ERREUR validation dates {table_name}: {e}")
            return False
    
    def validate_numeric_columns_fix(self, table_name):
        """
        VALIDATION 2: Vérifier que les colonnes numériques sont incluses
        """
        if table_name not in self.numeric_columns_fixed:
            return True
        
        logger.info(f"  VALIDATION 2: Colonnes numériques pour {table_name}")
        
        try:
            dwh_conn = self.connect_dwh()
            dwh_cursor = dwh_conn.cursor()
            
            for col in self.numeric_columns_fixed[table_name]:
                try:
                    # Vérifier que la colonne existe dans transform
                    dwh_cursor.execute(f"""
                        SELECT COUNT(*) FROM information_schema.columns 
                        WHERE table_schema = 'transform' 
                        AND table_name = '{table_name}_clean' 
                        AND column_name = '{col}'
                    """)
                    
                    col_exists = dwh_cursor.fetchone()[0] > 0
                    
                    if col_exists:
                        # Vérifier qu'elle a des données
                        dwh_cursor.execute(f'SELECT COUNT("{col}") FROM transform."{table_name}_clean" WHERE "{col}" IS NOT NULL')
                        non_null_count = dwh_cursor.fetchone()[0]
                        
                        logger.info(f"    SUCCES: {col} incluse avec {non_null_count} valeurs non-NULL")
                        self.validation_results['numeric_fixes_validated'] += 1
                    else:
                        logger.error(f"    ECHEC: {col} manquante dans transform")
                        self.validation_results['issues_found'].append(f"{table_name}.{col}: Missing in transform")
                        return False
                    
                except Exception as e:
                    logger.warning(f"    ERREUR validation {col}: {e}")
            
            dwh_conn.close()
            return True
            
        except Exception as e:
            logger.error(f"  ERREUR validation numériques {table_name}: {e}")
            return False
    
    def validate_user_normalization_fix(self, table_name):
        """
        VALIDATION 3: Vérifier que la normalisation utilisateur fonctionne
        """
        if table_name not in self.user_columns_normalized:
            return True
        
        logger.info(f"  VALIDATION 3: Normalisation utilisateurs pour {table_name}")
        
        try:
            dwh_conn = self.connect_dwh()
            dwh_cursor = dwh_conn.cursor()
            
            for user_col in self.user_columns_normalized[table_name]:
                try:
                    # Vérifier que les colonnes normalisées existent
                    expected_cols = [f'{user_col}_username', f'{user_col}_email', f'{user_col}_display_name']
                    
                    for norm_col in expected_cols:
                        dwh_cursor.execute(f"""
                            SELECT COUNT(*) FROM information_schema.columns 
                            WHERE table_schema = 'transform' 
                            AND table_name = '{table_name}_clean' 
                            AND column_name = '{norm_col}'
                        """)
                        
                        col_exists = dwh_cursor.fetchone()[0] > 0
                        
                        if col_exists:
                            # Vérifier qu'elle a des données
                            dwh_cursor.execute(f'SELECT COUNT("{norm_col}") FROM transform."{table_name}_clean" WHERE "{norm_col}" IS NOT NULL')
                            non_null_count = dwh_cursor.fetchone()[0]
                            
                            logger.info(f"    SUCCES: {norm_col} avec {non_null_count} valeurs")
                        else:
                            logger.error(f"    ECHEC: {norm_col} manquante")
                            self.validation_results['issues_found'].append(f"{table_name}.{norm_col}: Missing normalization")
                            return False
                    
                    self.validation_results['user_normalizations_validated'] += 1
                    
                except Exception as e:
                    logger.warning(f"    ERREUR validation {user_col}: {e}")
            
            dwh_conn.close()
            return True
            
        except Exception as e:
            logger.error(f"  ERREUR validation utilisateurs {table_name}: {e}")
            return False
    
    def validate_data_quality_scoring(self, table_name):
        """
        VALIDATION 4: Vérifier que le scoring qualité est réel
        """
        logger.info(f"  VALIDATION 4: Scoring qualité pour {table_name}")
        
        try:
            dwh_conn = self.connect_dwh()
            dwh_cursor = dwh_conn.cursor()
            
            # Vérifier que data_quality_score existe et varie
            dwh_cursor.execute(f"""
                SELECT 
                    MIN(data_quality_score) as min_score,
                    MAX(data_quality_score) as max_score,
                    AVG(data_quality_score) as avg_score,
                    COUNT(DISTINCT data_quality_score) as distinct_scores
                FROM transform."{table_name}_clean"
            """)
            
            result = dwh_cursor.fetchone()
            min_score, max_score, avg_score, distinct_scores = result
            
            # Vérifier que ce n'est pas juste 90% partout
            if distinct_scores > 1:
                logger.info(f"    SUCCES: Scoring varié (min={min_score}, max={max_score}, avg={avg_score:.1f})")
                self.validation_results['data_quality_validated'] += 1
            else:
                logger.warning(f"    ATTENTION: Scoring uniforme ({min_score})")
            
            dwh_conn.close()
            return True
            
        except Exception as e:
            logger.error(f"  ERREUR validation scoring {table_name}: {e}")
            return False
    
    def validate_record_counts(self, table_name):
        """
        VALIDATION 5: Vérifier que les counts sont cohérents
        """
        logger.info(f"  VALIDATION 5: Counts pour {table_name}")
        
        try:
            # Connexions
            jira_conn = self.connect_jira_readonly()
            jira_cursor = jira_conn.cursor()
            
            dwh_conn = self.connect_dwh()
            dwh_cursor = dwh_conn.cursor()
            
            # Comparer les counts
            jira_cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
            jiradb_count = jira_cursor.fetchone()[0]
            
            dwh_cursor.execute(f'SELECT COUNT(*) FROM staging."{table_name}"')
            staging_count = dwh_cursor.fetchone()[0]
            
            dwh_cursor.execute(f'SELECT COUNT(*) FROM transform."{table_name}_clean"')
            transform_count = dwh_cursor.fetchone()[0]
            
            logger.info(f"    JIRADB: {jiradb_count:,}")
            logger.info(f"    STAGING: {staging_count:,}")
            logger.info(f"    TRANSFORM: {transform_count:,}")
            
            # Validation
            if jiradb_count == staging_count == transform_count:
                logger.info(f"    SUCCES: Counts parfaitement alignés")
            elif staging_count == transform_count:
                logger.info(f"    SUCCES: Transform préserve staging")
            else:
                logger.warning(f"    ATTENTION: Différence de counts détectée")
                self.validation_results['issues_found'].append(f"{table_name}: Count mismatch")
            
            jira_conn.close()
            dwh_conn.close()
            return True
            
        except Exception as e:
            logger.error(f"  ERREUR validation counts {table_name}: {e}")
            return False
    
    def validate_table_complete(self, table_name):
        """
        Validation complète d'une table
        """
        logger.info(f"\nVALIDATION COMPLETE: {table_name}")
        logger.info("-" * 60)
        
        validations = [
            self.validate_date_conversion_fix(table_name),
            self.validate_numeric_columns_fix(table_name),
            self.validate_user_normalization_fix(table_name),
            self.validate_data_quality_scoring(table_name),
            self.validate_record_counts(table_name)
        ]
        
        success = all(validations)
        
        if success:
            logger.info(f"  RESULTAT: SUCCES COMPLET pour {table_name}")
            self.validation_results['tables_validated'] += 1
        else:
            logger.error(f"  RESULTAT: ECHECS détectés pour {table_name}")
        
        return success

    def run_complete_validation(self):
        """
        Exécuter la validation complète des 52 tables
        """
        logger.info("DEMARRAGE VALIDATION COMPLETE DES CORRECTIONS")
        logger.info("=" * 80)
        logger.info("VALIDATIONS A EFFECTUER:")
        logger.info("1. Date conversion: epoch → timestamp correct")
        logger.info("2. Numeric columns: Inclusion des colonnes numériques")
        logger.info("3. User normalization: JOIN avec cwd_user")
        logger.info("4. Data quality: Scoring réel (non fixe)")
        logger.info("5. Record counts: Cohérence JIRADB → STAGING → TRANSFORM")
        logger.info("=" * 80)

        success_count = 0
        failed_tables = []

        for i, table_name in enumerate(self.critical_tables_52, 1):
            logger.info(f"\n[{i:2d}/52] VALIDATION: {table_name}")

            if self.validate_table_complete(table_name):
                success_count += 1
                logger.info(f"  SUCCES: {table_name}")
            else:
                failed_tables.append(table_name)
                logger.error(f"  ECHEC: {table_name}")

        # Calcul du taux de succès
        self.validation_results['success_rate'] = (success_count / len(self.critical_tables_52)) * 100

        # Résumé final
        logger.info(f"\nVALIDATION COMPLETE TERMINEE!")
        logger.info("=" * 80)
        logger.info(f"Tables validées: {success_count}/{len(self.critical_tables_52)}")
        logger.info(f"Taux de succès: {self.validation_results['success_rate']:.1f}%")
        logger.info(f"Corrections de dates validées: {self.validation_results['date_fixes_validated']}")
        logger.info(f"Corrections numériques validées: {self.validation_results['numeric_fixes_validated']}")
        logger.info(f"Normalisations utilisateur validées: {self.validation_results['user_normalizations_validated']}")
        logger.info(f"Scoring qualité validé: {self.validation_results['data_quality_validated']} tables")
        logger.info(f"Problèmes détectés: {len(self.validation_results['issues_found'])}")

        if self.validation_results['issues_found']:
            logger.warning(f"\nPROBLEMES DETECTES:")
            for issue in self.validation_results['issues_found']:
                logger.warning(f"  - {issue}")

        if failed_tables:
            logger.warning(f"\nTABLES AVEC ECHECS:")
            for table in failed_tables:
                logger.warning(f"  - {table}")

        if success_count == len(self.critical_tables_52):
            logger.info(f"\nSUCCES COMPLET! Toutes les corrections validées")
            return True
        else:
            logger.warning(f"\nVALIDATION PARTIELLE: {success_count}/{len(self.critical_tables_52)} tables")
            return False

def main():
    """Point d'entrée principal"""
    print("VALIDATION COMPLETE DES CORRECTIONS - 52 TABLES")
    print("=" * 60)
    print("Vérification que tous les problèmes ont été corrigés:")
    print("✅ Date conversion: epoch → timestamp")
    print("✅ Numeric columns: Inclusion complète")
    print("✅ User normalization: JOIN cwd_user")
    print("✅ Data quality: Scoring réel")
    print("✅ Record counts: Cohérence totale")
    print("=" * 60)

    validator = ValidateCompleteFix()

    try:
        success = validator.run_complete_validation()

        if success:
            print(f"\nVALIDATION REUSSIE!")
            print(f"Toutes les corrections fonctionnent parfaitement")
            print(f"Les 52 tables sont prêtes pour la phase LOAD")
            return 0
        else:
            print(f"\nVALIDATION PARTIELLE!")
            print(f"Voir les logs pour les détails des problèmes")
            return 1

    except Exception as e:
        logger.error(f"Erreur fatale: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
