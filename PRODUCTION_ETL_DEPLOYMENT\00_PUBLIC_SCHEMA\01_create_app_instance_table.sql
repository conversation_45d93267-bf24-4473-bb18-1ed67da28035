-- 🏗️ CREATE APP_INSTANCE TABLE
-- Stores Jira instance connection information for the analytics app

CREATE TABLE IF NOT EXISTS public.app_instance (
    instance_id NUMERIC PRIMARY KEY,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL DEFAULT 5432,
    db_name VARCHAR(255) NOT NULL,
    db_user VARCHAR(255) NOT NULL,
    db_password VARCHAR(255) NOT NULL,
    client_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_sync TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default instance (modify as needed)
INSERT INTO public.app_instance (
    instance_id, host, port, db_name, db_user, db_password, client_name
) VALUES (
    1, 'localhost', 5432, 'jiradb', 'jirauser', 'mypassword', 'default_client'
) ON CONFLICT (instance_id) DO NOTHING;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_app_instance_host_db ON public.app_instance(host, db_name);

COMMENT ON TABLE public.app_instance IS 'Jira instance connection configuration for analytics app';
