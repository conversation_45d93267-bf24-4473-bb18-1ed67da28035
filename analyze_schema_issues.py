#!/usr/bin/env python3
"""
🔍 ANALYZE SCHEMA ISSUES
Analyze data type inconsistencies and missing foreign keys in the star schema

WHAT IT CHECKS:
1. Column data type mismatches between schemas
2. Missing foreign key relationships in fact tables
3. Star schema integrity issues
4. Data type consistency problems
"""

import psycopg2
from psycopg2.extras import RealDictCursor
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def connect_to_database(dbname):
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database=dbname,
            user="jirauser",
            password="mypassword"
        )
        return conn
    except Exception as e:
        logger.error(f"Error connecting to {dbname}: {e}")
        return None

def get_table_columns(conn, schema_name, table_name):
    """Get column information for a table"""
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    
    cursor.execute("""
        SELECT 
            column_name,
            data_type,
            character_maximum_length,
            numeric_precision,
            numeric_scale,
            is_nullable
        FROM information_schema.columns 
        WHERE table_schema = %s AND table_name = %s
        ORDER BY ordinal_position
    """, (schema_name, table_name))
    
    columns = cursor.fetchall()
    cursor.close()
    return columns

def analyze_data_type_consistency():
    """Analyze data type consistency across schemas"""
    logger.info("🔍 ANALYZING DATA TYPE CONSISTENCY")
    logger.info("="*60)
    
    conn = connect_to_database("aya")
    if not conn:
        return
    
    # Get all tables that exist in multiple schemas
    cursor = conn.cursor()
    cursor.execute("""
        SELECT DISTINCT 
            REPLACE(t1.table_name, '_clean', '') as base_table_name,
            t1.table_name as staging_table,
            t2.table_name as transform_table
        FROM information_schema.tables t1
        JOIN information_schema.tables t2 ON REPLACE(t2.table_name, '_clean', '') = REPLACE(t1.table_name, '_clean', '')
        WHERE t1.table_schema = 'staging' 
        AND t2.table_schema = 'transform'
        AND t2.table_name LIKE '%_clean'
        ORDER BY base_table_name
        LIMIT 10
    """)
    
    table_pairs = cursor.fetchall()
    cursor.close()
    
    issues_found = []
    
    for base_name, staging_table, transform_table in table_pairs:
        logger.info(f"\n📋 Checking: {base_name}")
        
        # Get columns from both schemas
        staging_cols = get_table_columns(conn, 'staging', staging_table)
        transform_cols = get_table_columns(conn, 'transform', transform_table)
        
        # Create lookup dictionaries
        staging_dict = {col['column_name']: col for col in staging_cols}
        transform_dict = {col['column_name']: col for col in transform_cols}
        
        # Find common columns with different types
        for col_name in staging_dict:
            if col_name in transform_dict:
                staging_type = staging_dict[col_name]['data_type']
                transform_type = transform_dict[col_name]['data_type']
                
                if staging_type != transform_type:
                    issue = {
                        'table': base_name,
                        'column': col_name,
                        'staging_type': staging_type,
                        'transform_type': transform_type
                    }
                    issues_found.append(issue)
                    logger.warning(f"  ⚠️ {col_name}: {staging_type} → {transform_type}")
    
    conn.close()
    
    logger.info(f"\n📊 SUMMARY: {len(issues_found)} data type inconsistencies found")
    return issues_found

def analyze_star_schema_fks():
    """Analyze missing foreign keys in star schema"""
    logger.info("\n🌟 ANALYZING STAR SCHEMA FOREIGN KEYS")
    logger.info("="*60)
    
    conn = connect_to_database("aya")
    if not conn:
        return
    
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    
    # Get fact tables (should have FKs to dimension tables)
    cursor.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'load' 
        AND table_name LIKE 'fact_%'
        ORDER BY table_name
    """)
    
    fact_tables = [row['table_name'] for row in cursor.fetchall()]
    
    # Get dimension tables
    cursor.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'load' 
        AND table_name LIKE 'dim_%'
        ORDER BY table_name
    """)
    
    dim_tables = [row['table_name'] for row in cursor.fetchall()]
    
    logger.info(f"📊 Found {len(fact_tables)} fact tables and {len(dim_tables)} dimension tables")
    
    # Check existing foreign keys
    cursor.execute("""
        SELECT 
            tc.table_name,
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name
        FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_schema = 'load'
    """)
    
    existing_fks = cursor.fetchall()
    
    logger.info(f"\n🔗 EXISTING FOREIGN KEYS: {len(existing_fks)}")
    for fk in existing_fks:
        logger.info(f"  ✅ {fk['table_name']}.{fk['column_name']} → {fk['foreign_table_name']}.{fk['foreign_column_name']}")
    
    # Analyze potential FK relationships
    missing_fks = []
    
    for fact_table in fact_tables:
        logger.info(f"\n📋 Analyzing fact table: {fact_table}")
        
        # Get columns in fact table
        fact_cols = get_table_columns(conn, 'load', fact_table)
        
        for col in fact_cols:
            col_name = col['column_name']
            
            # Look for potential FK columns (ending with _id or _key)
            if col_name.endswith('_id') or col_name.endswith('_key'):
                # Find matching dimension table
                potential_dim = None
                
                if col_name.endswith('_id'):
                    base_name = col_name[:-3]  # Remove '_id'
                    potential_dim = f"dim_{base_name}"
                elif col_name.endswith('_key'):
                    base_name = col_name[:-4]  # Remove '_key'
                    potential_dim = f"dim_{base_name}"
                
                if potential_dim in dim_tables:
                    # Check if FK already exists
                    fk_exists = any(
                        fk['table_name'] == fact_table and 
                        fk['column_name'] == col_name and
                        fk['foreign_table_name'] == potential_dim
                        for fk in existing_fks
                    )
                    
                    if not fk_exists:
                        missing_fks.append({
                            'fact_table': fact_table,
                            'fact_column': col_name,
                            'dim_table': potential_dim,
                            'dim_column': 'id'  # Assuming 'id' is the PK
                        })
                        logger.warning(f"  ⚠️ Missing FK: {col_name} → {potential_dim}.id")
    
    cursor.close()
    conn.close()
    
    logger.info(f"\n📊 SUMMARY: {len(missing_fks)} missing foreign keys found")
    return missing_fks

def analyze_primary_keys():
    """Analyze primary key issues"""
    logger.info("\n🔑 ANALYZING PRIMARY KEYS")
    logger.info("="*60)
    
    conn = connect_to_database("aya")
    if not conn:
        return
    
    cursor = conn.cursor(cursor_factory=RealDictCursor)
    
    # Check for tables without primary keys
    cursor.execute("""
        SELECT t.table_name
        FROM information_schema.tables t
        LEFT JOIN information_schema.table_constraints tc 
            ON t.table_name = tc.table_name 
            AND t.table_schema = tc.table_schema
            AND tc.constraint_type = 'PRIMARY KEY'
        WHERE t.table_schema = 'load'
        AND tc.constraint_name IS NULL
        ORDER BY t.table_name
    """)
    
    tables_without_pk = [row['table_name'] for row in cursor.fetchall()]
    
    logger.info(f"📊 Tables without PRIMARY KEY: {len(tables_without_pk)}")
    for table in tables_without_pk:
        logger.warning(f"  ⚠️ {table}")
    
    cursor.close()
    conn.close()
    
    return tables_without_pk

def main():
    """Main analysis function"""
    logger.info("🔍 COMPREHENSIVE SCHEMA ANALYSIS")
    logger.info("="*70)
    logger.info("Analyzing AYA database for schema issues...")
    logger.info("="*70)
    
    # 1. Analyze data type consistency
    type_issues = analyze_data_type_consistency()
    
    # 2. Analyze star schema foreign keys
    missing_fks = analyze_star_schema_fks()
    
    # 3. Analyze primary keys
    missing_pks = analyze_primary_keys()
    
    # 4. Summary
    logger.info("\n" + "="*70)
    logger.info("📊 ANALYSIS SUMMARY")
    logger.info("="*70)
    logger.info(f"🔧 Data type issues: {len(type_issues)}")
    logger.info(f"🔗 Missing foreign keys: {len(missing_fks)}")
    logger.info(f"🔑 Missing primary keys: {len(missing_pks)}")
    
    total_issues = len(type_issues) + len(missing_fks) + len(missing_pks)
    logger.info(f"⚠️ Total issues: {total_issues}")
    
    if total_issues > 0:
        logger.info("\n🎯 NEXT STEPS:")
        logger.info("1. Create data type correction script")
        logger.info("2. Create foreign key creation script")
        logger.info("3. Create primary key creation script")
        logger.info("4. Add these to PRODUCTION_ETL_DEPLOYMENT")
    else:
        logger.info("\n✅ No issues found - schema is perfect!")
    
    return {
        'type_issues': type_issues,
        'missing_fks': missing_fks,
        'missing_pks': missing_pks
    }

if __name__ == "__main__":
    results = main()
