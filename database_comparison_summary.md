# DATABASE COMPARISON SUMMARY
## AYA (Production) vs TEST Database

### OVERVIEW
- **AYA Database**: Fully populated production database with complete ETL pipeline
- **TEST Database**: Empty test database with minimal setup

---

## SCHEMA COMPARISON

### 1. PUBLIC SCHEMA
| Database | Tables | Total Records |
|----------|--------|---------------|
| **AYA**  | 16     | 5,242         |
| **TEST** | 1      | 1             |

**AYA ONLY Tables (15 tables):**
- `app_instance_backup` (1 rows)
- **Dimension Tables (10 empty):** dim_attachments, dim_components, dim_custom_fields, dim_groups, dim_issue_comments, dim_project_roles, dim_versions, dim_workflow_schemes, dim_workflow_statuses, dim_workflow_transitions
- **Populated Dimension Tables (2):** dim_projects (19), dim_users (49)
- **Fact Tables (2):** fact_issues (3,805), fact_worklogs (1,368)

**Common Tables:**
- `app_instance` ✓ (1 vs 1 rows - identical)

---

### 2. STAGING SCHEMA
| Database | Tables | Total Records |
|----------|--------|---------------|
| **AYA**  | 52     | 72,025        |
| **TEST** | 0      | 0             |

**Status:** AYA has complete staging schema with all 52 Jira tables extracted from jiradb. TEST has no staging schema.

**Key Tables in AYA Staging:**
- **Core Jira Tables:** jiraissue (2,335), changegroup (13,697), changeitem (20,009), worklog (6,315)
- **User Management:** cwd_user (49), app_user (49), cwd_user_attributes (255)
- **Project Data:** project (19), component (139), projectversion (137)
- **Custom Fields:** customfield (32), customfieldvalue (10,317)
- **Plugin Tables:** AO_* tables for Script Runner, Agile, SQL Query, Audit Log

---

### 3. TRANSFORM SCHEMA
| Database | Tables | Total Records |
|----------|--------|---------------|
| **AYA**  | 52     | 72,025        |
| **TEST** | 0      | 0             |

**Status:** AYA has complete transform schema with all 52 cleaned tables (suffix "_clean"). TEST has no transform schema.

**Transformations Applied in AYA:**
- User normalization (JIRAUSER format extraction)
- NULL column removal (46 columns dropped)
- Data quality improvements
- Column type consistency fixes

---

### 4. LOAD SCHEMA
| Database | Tables | Total Records |
|----------|--------|---------------|
| **AYA**  | 40     | 44,499        |
| **TEST** | 0      | 0             |

**Status:** AYA has complete load schema with star schema design. TEST has no load schema.

**AYA Load Schema Structure:**
- **39 Dimension Tables:** dim_* (users, projects, issues_metadata, dates, etc.)
- **1 Fact Table:** fact_issues (2,335 rows)
- **Data Population:** 30/40 tables populated (75% complete)

---

## KEY DIFFERENCES SUMMARY

### 1. **PRODUCTION vs EMPTY**
- **AYA**: Complete 3-tier ETL pipeline (staging → transform → load)
- **TEST**: Empty database with only basic app_instance table

### 2. **DATA VOLUME**
- **AYA Total Records**: ~193,791 across all schemas
- **TEST Total Records**: 1 (only app_instance)

### 3. **ETL PIPELINE STATUS**
- **AYA**: 100% staging, 100% transform, 75% load completion
- **TEST**: 0% - no ETL implementation

### 4. **SCHEMA MATURITY**
- **AYA**: Production-ready with optimized schemas
- **TEST**: Development/testing setup only

---

## RECOMMENDATIONS

1. **For Testing**: Copy AYA ETL scripts to populate TEST database
2. **For Development**: Use TEST for ETL script development/validation
3. **For Production**: AYA is ready for analytics and reporting
4. **Missing in TEST**: All ETL schemas need to be created and populated

---

## NEXT STEPS

1. **Populate TEST Database**: Run ETL scripts to create staging/transform/load schemas
2. **Complete AYA Load Schema**: Populate remaining 10 empty dimension tables
3. **Validation**: Ensure TEST matches AYA structure for development consistency
