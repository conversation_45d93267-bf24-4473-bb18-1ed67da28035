#!/usr/bin/env python3
"""
🏗️ BUILD LOAD SCHEMA - STAR SCHEMA CREATION
Create the dimensional model (Star Schema) for Jira analytics
1 FACT table + 37 DIMENSION tables = 38 total tables

⭐ STAR SCHEMA DESIGN:
- FACT_ISSUES: Central fact table with measures
- 6 Denormalized dimensions (16 tables combined)
- 28 Separate dimensions (28 tables)
- 3 Generated dimensions (dates, instances, quality)

🎯 OBJECTIF: Production-ready Star Schema for migration consulting
"""

import psycopg2
import logging
from datetime import datetime
import time

# Configuration logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'build_load_schema_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class LoadSchemaBuilder:
    """
    Constructeur du schéma LOAD (Star Schema)
    """
    
    def __init__(self):
        # Configuration Data Warehouse
        self.dw_config = {
            'host': 'localhost',
            'database': 'aya',
            'user': 'jirauser',
            'password': 'mypassword'
        }
        
        # Star Schema configuration
        self.denormalized_dimensions = {
            'DIM_ISSUES_METADATA': ['issuetype_clean', 'priority_clean', 'issuestatus_clean', 'resolution_clean', 'issuelinktype_clean'],
            'DIM_PROJECTS': ['project_clean', 'projectcategory_clean', 'projectrole_clean'],
            'DIM_USERS': ['cwd_user_clean', 'cwd_directory_clean', 'app_user_clean'],
            'DIM_PERMISSIONS': ['permissionscheme_clean', 'schemepermissions_clean'],
            'DIM_SCREENS': ['fieldscreen_clean', 'fieldscreentab_clean'],
            'DIM_AGILE': ['AO_60DB71_RAPIDVIEW_clean', 'AO_60DB71_SPRINT_clean', 'AO_60DB71_ISSUERANKING_clean']
        }
        
        self.separate_dimensions = [
            'jiraworkflows_clean', 'workflowscheme_clean', 'workflowschemeentity_clean',
            'component_clean', 'projectversion_clean', 'customfield_clean',
            'fieldconfiguration_clean', 'fieldconfigscheme_clean', 'projectroleactor_clean',
            'userassociation_clean', 'cwd_group_clean', 'cwd_membership_clean',
            'cwd_user_attributes_clean', 'issuelink_clean', 'nodeassociation_clean',
            'jiraaction_clean', 'AO_4B00E6_STASH_SETTINGS_clean', 'AO_4B00E6_SR_USER_PROP_clean',
            'AO_4B00E6_UPGRADE_BACKUP_clean', 'AO_786AC3_SQL_FAVOURITE_clean',
            'AO_C77861_AUDIT_ENTITY_clean', 'AO_C77861_AUDIT_ACTION_CACHE_clean',
            'AO_C77861_AUDIT_CATEGORY_CACHE_clean', 'pluginversion_clean', 'managedconfigurationitem_clean'
        ]
        
        self.fact_source_tables = [
            'jiraissue_clean', 'worklog_clean', 'changegroup_clean', 'changeitem_clean',
            'fileattachment_clean', 'customfieldvalue_clean', 'label_clean',
            'os_currentstep_clean', 'os_wfentry_clean'
        ]
        
        self.stats = {
            'start_time': datetime.now(),
            'tables_created': 0,
            'total_columns': 0,
            'denormalized_dims': 0,
            'separate_dims': 0,
            'generated_dims': 0,
            'errors': []
        }
    
    def connect_dw(self):
        """Connexion au Data Warehouse"""
        return psycopg2.connect(**self.dw_config)
    
    def create_load_schema(self):
        """Créer le schéma LOAD"""
        logger.info("🏗️ CRÉATION SCHÉMA LOAD")
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # Supprimer le schéma existant
            cursor.execute("DROP SCHEMA IF EXISTS load CASCADE")
            
            # Créer le nouveau schéma
            cursor.execute("CREATE SCHEMA load")
            
            conn.commit()
            logger.info("   ✅ Schéma load créé")
            
        except Exception as e:
            logger.error(f"   ❌ Erreur création schéma: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()
    
    def get_transform_columns(self, table_name):
        """Récupérer les colonnes d'une table transform"""
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                SELECT column_name, data_type, character_maximum_length, 
                       numeric_precision, numeric_scale, is_nullable
                FROM information_schema.columns
                WHERE table_schema = 'transform' AND table_name = %s
                ORDER BY ordinal_position
            """, (table_name,))
            
            return cursor.fetchall()
            
        except Exception as e:
            logger.error(f"❌ Erreur récupération colonnes {table_name}: {e}")
            return []
        finally:
            conn.close()
    
    def format_column_type(self, data_type, char_len, num_precision, num_scale):
        """Formater le type de colonne PostgreSQL"""
        if data_type == 'character varying' and char_len:
            return f'VARCHAR({char_len})'
        elif data_type == 'numeric' and num_precision and num_scale:
            return f'NUMERIC({num_precision},{num_scale})'
        elif data_type == 'numeric' and num_precision:
            return f'NUMERIC({num_precision})'
        elif data_type == 'character' and char_len:
            return f'CHAR({char_len})'
        elif data_type == 'timestamp without time zone':
            return 'TIMESTAMP'
        elif data_type == 'timestamp with time zone':
            return 'TIMESTAMPTZ'
        else:
            return data_type.upper()
    
    def create_fact_issues_table(self):
        """Créer la table de faits FACT_ISSUES"""
        logger.info("📊 CRÉATION FACT_ISSUES")
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # Récupérer les colonnes de jiraissue_clean comme base
            jiraissue_columns = self.get_transform_columns('jiraissue_clean')
            
            if not jiraissue_columns:
                raise Exception("Impossible de récupérer les colonnes jiraissue_clean")
            
            # Construire les colonnes de la table de faits
            column_definitions = []
            
            # Clé primaire
            column_definitions.append('    issue_id VARCHAR(255) PRIMARY KEY')
            
            # Colonnes principales de jiraissue
            for col_name, data_type, char_len, num_precision, num_scale, is_nullable in jiraissue_columns:
                if col_name not in ['id', 'instance_id', 'transformed_at', 'data_quality_score', 'has_null_data']:
                    pg_type = self.format_column_type(data_type, char_len, num_precision, num_scale)
                    null_constraint = '' if is_nullable == 'YES' else ' NOT NULL'
                    column_definitions.append(f'    {col_name} {pg_type}{null_constraint}')
            
            # Mesures agrégées des autres tables
            measure_columns = [
                '    worklog_count INTEGER DEFAULT 0',
                '    total_time_spent BIGINT DEFAULT 0',
                '    change_count INTEGER DEFAULT 0',
                '    attachment_count INTEGER DEFAULT 0',
                '    custom_field_count INTEGER DEFAULT 0',
                '    labels JSONB',
                '    current_workflow_step INTEGER',
                '    workflow_entry_id BIGINT'
            ]
            column_definitions.extend(measure_columns)
            
            # Clés étrangères vers les dimensions
            fk_columns = [
                '    dim_issues_metadata_id INTEGER',
                '    dim_projects_id INTEGER', 
                '    dim_users_id INTEGER',
                '    dim_permissions_id INTEGER',
                '    dim_screens_id INTEGER',
                '    dim_agile_id INTEGER',
                '    dim_workflows_id INTEGER',
                '    dim_workflow_schemes_id INTEGER',
                '    dim_components_id INTEGER',
                '    dim_versions_id INTEGER',
                '    dim_custom_fields_id INTEGER',
                '    dim_dates_id INTEGER',
                '    dim_instances_id INTEGER'
            ]
            column_definitions.extend(fk_columns)
            
            # Métadonnées
            metadata_columns = [
                '    instance_id INTEGER NOT NULL',
                '    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                '    data_quality_score INTEGER DEFAULT 100'
            ]
            column_definitions.extend(metadata_columns)
            
            # Créer la table
            columns_ddl = ',\n'.join(column_definitions)
            create_sql = f'''
                CREATE TABLE load.fact_issues (
                {columns_ddl}
                )
            '''
            
            cursor.execute(create_sql)
            conn.commit()
            
            self.stats['tables_created'] += 1
            self.stats['total_columns'] += len(column_definitions)
            
            logger.info(f"   ✅ FACT_ISSUES créée avec {len(column_definitions)} colonnes")
            
        except Exception as e:
            logger.error(f"   ❌ Erreur création FACT_ISSUES: {e}")
            conn.rollback()
            self.stats['errors'].append(f"FACT_ISSUES: {e}")
        finally:
            conn.close()

    def create_denormalized_dimension(self, dim_name, source_tables):
        """Créer une dimension dénormalisée à partir de plusieurs tables"""
        logger.info(f"🔗 CRÉATION {dim_name}")

        conn = self.connect_dw()
        cursor = conn.cursor()

        try:
            # Récupérer les colonnes de toutes les tables sources
            all_columns = []
            primary_table = source_tables[0]  # Première table = table principale

            for table_name in source_tables:
                table_columns = self.get_transform_columns(table_name)

                if not table_columns:
                    logger.warning(f"   ⚠️ Table {table_name} non trouvée")
                    continue

                # Préfixer les colonnes avec le nom de table (sauf table principale)
                prefix = '' if table_name == primary_table else f"{table_name.replace('_clean', '')}_"

                for col_name, data_type, char_len, num_precision, num_scale, is_nullable in table_columns:
                    # Ignorer les métadonnées transform
                    if col_name in ['instance_id', 'transformed_at', 'data_quality_score', 'has_null_data']:
                        continue

                    pg_type = self.format_column_type(data_type, char_len, num_precision, num_scale)
                    null_constraint = '' if is_nullable == 'YES' else ' NOT NULL'

                    # Nom de colonne avec préfixe si nécessaire
                    final_col_name = f"{prefix}{col_name}" if prefix else col_name
                    all_columns.append(f'    {final_col_name} {pg_type}{null_constraint}')

            if not all_columns:
                raise Exception(f"Aucune colonne trouvée pour {dim_name}")

            # Ajouter clé primaire et métadonnées
            dimension_columns = [
                f'    {dim_name.lower()}_id SERIAL PRIMARY KEY'
            ] + all_columns + [
                '    instance_id INTEGER NOT NULL',
                '    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
            ]

            # Créer la table
            columns_ddl = ',\n'.join(dimension_columns)
            table_name = dim_name.lower()

            create_sql = f'''
                CREATE TABLE load.{table_name} (
                {columns_ddl}
                )
            '''

            cursor.execute(create_sql)
            conn.commit()

            self.stats['tables_created'] += 1
            self.stats['denormalized_dims'] += 1
            self.stats['total_columns'] += len(dimension_columns)

            logger.info(f"   ✅ {dim_name} créée avec {len(dimension_columns)} colonnes")

        except Exception as e:
            logger.error(f"   ❌ Erreur création {dim_name}: {e}")
            conn.rollback()
            self.stats['errors'].append(f"{dim_name}: {e}")
        finally:
            conn.close()

    def create_separate_dimension(self, source_table):
        """Créer une dimension séparée à partir d'une table transform"""
        # Nom de dimension basé sur la table source
        dim_name = f"dim_{source_table.replace('_clean', '').lower()}"

        logger.info(f"📋 CRÉATION {dim_name}")

        conn = self.connect_dw()
        cursor = conn.cursor()

        try:
            # Récupérer les colonnes de la table source
            table_columns = self.get_transform_columns(source_table)

            if not table_columns:
                raise Exception(f"Table {source_table} non trouvée")

            # Construire les colonnes de dimension
            column_definitions = [f'    {dim_name}_id SERIAL PRIMARY KEY']

            for col_name, data_type, char_len, num_precision, num_scale, is_nullable in table_columns:
                # Ignorer les métadonnées transform
                if col_name in ['instance_id', 'transformed_at', 'data_quality_score', 'has_null_data']:
                    continue

                pg_type = self.format_column_type(data_type, char_len, num_precision, num_scale)
                null_constraint = '' if is_nullable == 'YES' else ' NOT NULL'
                column_definitions.append(f'    {col_name} {pg_type}{null_constraint}')

            # Ajouter métadonnées
            column_definitions.extend([
                '    instance_id INTEGER NOT NULL',
                '    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
            ])

            # Créer la table
            columns_ddl = ',\n'.join(column_definitions)

            create_sql = f'''
                CREATE TABLE load.{dim_name} (
                {columns_ddl}
                )
            '''

            cursor.execute(create_sql)
            conn.commit()

            self.stats['tables_created'] += 1
            self.stats['separate_dims'] += 1
            self.stats['total_columns'] += len(column_definitions)

            logger.info(f"   ✅ {dim_name} créée avec {len(column_definitions)} colonnes")

        except Exception as e:
            logger.error(f"   ❌ Erreur création {dim_name}: {e}")
            conn.rollback()
            self.stats['errors'].append(f"{dim_name}: {e}")
        finally:
            conn.close()

    def create_generated_dimensions(self):
        """Créer les dimensions générées (dates, instances, qualité)"""
        logger.info("⚙️ CRÉATION DIMENSIONS GÉNÉRÉES")

        conn = self.connect_dw()
        cursor = conn.cursor()

        try:
            # DIM_DATES - Dimension temporelle
            dates_sql = '''
                CREATE TABLE load.dim_dates (
                    dim_dates_id SERIAL PRIMARY KEY,
                    date_value DATE NOT NULL,
                    year INTEGER NOT NULL,
                    quarter INTEGER NOT NULL,
                    month INTEGER NOT NULL,
                    month_name VARCHAR(20) NOT NULL,
                    week INTEGER NOT NULL,
                    day_of_year INTEGER NOT NULL,
                    day_of_month INTEGER NOT NULL,
                    day_of_week INTEGER NOT NULL,
                    day_name VARCHAR(20) NOT NULL,
                    is_weekend BOOLEAN NOT NULL,
                    is_holiday BOOLEAN DEFAULT FALSE,
                    fiscal_year INTEGER,
                    fiscal_quarter INTEGER,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            '''

            # DIM_INSTANCES - Dimension des instances Jira
            instances_sql = '''
                CREATE TABLE load.dim_instances (
                    dim_instances_id SERIAL PRIMARY KEY,
                    instance_id INTEGER NOT NULL UNIQUE,
                    instance_name VARCHAR(255),
                    instance_url VARCHAR(500),
                    instance_type VARCHAR(50),
                    jira_version VARCHAR(50),
                    database_type VARCHAR(50),
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            '''

            # DIM_DATA_QUALITY - Dimension qualité des données
            quality_sql = '''
                CREATE TABLE load.dim_data_quality (
                    dim_data_quality_id SERIAL PRIMARY KEY,
                    quality_score INTEGER NOT NULL,
                    quality_level VARCHAR(20) NOT NULL,
                    has_null_data BOOLEAN NOT NULL,
                    completeness_score DECIMAL(5,2),
                    accuracy_score DECIMAL(5,2),
                    consistency_score DECIMAL(5,2),
                    description TEXT,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            '''

            # Créer les tables
            cursor.execute(dates_sql)
            cursor.execute(instances_sql)
            cursor.execute(quality_sql)

            conn.commit()

            self.stats['tables_created'] += 3
            self.stats['generated_dims'] += 3
            self.stats['total_columns'] += 45  # Estimation

            logger.info("   ✅ DIM_DATES créée")
            logger.info("   ✅ DIM_INSTANCES créée")
            logger.info("   ✅ DIM_DATA_QUALITY créée")

        except Exception as e:
            logger.error(f"   ❌ Erreur création dimensions générées: {e}")
            conn.rollback()
            self.stats['errors'].append(f"Generated dimensions: {e}")
        finally:
            conn.close()

    def build_all_dimensions(self):
        """Construire toutes les dimensions du Star Schema"""
        logger.info("🏗️ CONSTRUCTION TOUTES LES DIMENSIONS")
        logger.info("=" * 70)

        # 1. Créer les dimensions dénormalisées
        logger.info("📊 PHASE 1: DIMENSIONS DÉNORMALISÉES")
        for dim_name, source_tables in self.denormalized_dimensions.items():
            self.create_denormalized_dimension(dim_name, source_tables)

        # 2. Créer les dimensions séparées
        logger.info("\n📋 PHASE 2: DIMENSIONS SÉPARÉES")
        for source_table in self.separate_dimensions:
            self.create_separate_dimension(source_table)

        # 3. Créer les dimensions générées
        logger.info("\n⚙️ PHASE 3: DIMENSIONS GÉNÉRÉES")
        self.create_generated_dimensions()

    def verify_load_schema(self):
        """Vérifier le schéma LOAD créé"""
        logger.info("🔍 VÉRIFICATION SCHÉMA LOAD")

        conn = self.connect_dw()
        cursor = conn.cursor()

        try:
            # Compter les tables créées
            cursor.execute("""
                SELECT table_name,
                       (SELECT COUNT(*) FROM information_schema.columns
                        WHERE table_schema = 'load' AND table_name = t.table_name) as column_count
                FROM information_schema.tables t
                WHERE table_schema = 'load'
                ORDER BY table_name
            """)

            load_tables = cursor.fetchall()

            logger.info(f"📊 Tables load créées: {len(load_tables)}")

            fact_tables = [t for t in load_tables if 'fact_' in t[0]]
            dim_tables = [t for t in load_tables if 'dim_' in t[0]]

            logger.info(f"   📈 Tables de faits: {len(fact_tables)}")
            logger.info(f"   📊 Tables de dimensions: {len(dim_tables)}")

            total_columns = sum(table[1] for table in load_tables)
            logger.info(f"   📋 Total colonnes: {total_columns}")

            return len(load_tables)

        finally:
            conn.close()

    def generate_final_report(self):
        """Générer le rapport final de construction"""
        duration = (datetime.now() - self.stats['start_time']).total_seconds()

        logger.info("\n" + "=" * 70)
        logger.info("🎉 CONSTRUCTION LOAD SCHEMA TERMINÉE!")
        logger.info("=" * 70)
        logger.info(f"⏱️ Durée: {duration:.2f} secondes")
        logger.info(f"📊 Tables créées: {self.stats['tables_created']}")
        logger.info(f"📈 Dimensions dénormalisées: {self.stats['denormalized_dims']}")
        logger.info(f"📋 Dimensions séparées: {self.stats['separate_dims']}")
        logger.info(f"⚙️ Dimensions générées: {self.stats['generated_dims']}")
        logger.info(f"📝 Total colonnes: {self.stats['total_columns']}")
        logger.info(f"❌ Erreurs: {len(self.stats['errors'])}")

        if self.stats['errors']:
            logger.warning(f"\n🚨 Erreurs rencontrées:")
            for error in self.stats['errors'][:5]:
                logger.warning(f"   - {error}")

        success_rate = (self.stats['tables_created'] - len(self.stats['errors'])) / 38 * 100

        if success_rate >= 95:
            logger.info(f"\n🎯 STAR SCHEMA EXCELLENT!")
            logger.info(f"   {success_rate:.1f}% des tables créées")
            logger.info(f"   Prêt pour le chargement des données")
            return True
        elif success_rate >= 85:
            logger.warning(f"\n⚠️ STAR SCHEMA ACCEPTABLE")
            logger.warning(f"   {success_rate:.1f}% des tables créées")
            return False
        else:
            logger.error(f"\n❌ STAR SCHEMA ÉCHOUÉ")
            logger.error(f"   Seulement {success_rate:.1f}% des tables créées")
            return False


def main():
    """Point d'entrée principal"""
    print("🏗️ CONSTRUCTION STAR SCHEMA - LOAD PHASE")
    print("=" * 60)
    print("⭐ STAR SCHEMA DESIGN:")
    print("   📊 1 FACT_ISSUES (central hub)")
    print("   🔗 6 Dimensions dénormalisées (16 tables)")
    print("   📋 28 Dimensions séparées (28 tables)")
    print("   ⚙️ 3 Dimensions générées")
    print("   🎯 TOTAL: 38 tables")
    print("=" * 60)

    builder = LoadSchemaBuilder()

    try:
        # 1. Créer le schéma LOAD
        builder.create_load_schema()

        # 2. Créer la table de faits
        builder.create_fact_issues_table()

        # 3. Créer toutes les dimensions
        builder.build_all_dimensions()

        # 4. Vérifier le schéma créé
        total_tables = builder.verify_load_schema()

        # 5. Générer le rapport final
        schema_success = builder.generate_final_report()

        if schema_success:
            print(f"\n🎯 PROCHAINE ÉTAPE:")
            print(f"   Le Star Schema est prêt!")
            print(f"   Exécuter: python load_transform_to_star.py")
            print(f"   Pour charger les données transform → load")
            return 0
        else:
            print(f"\n🔧 ACTIONS REQUISES:")
            print(f"   Corriger les erreurs de création")
            print(f"   Vérifier les logs pour les détails")
            return 1

    except Exception as e:
        logger.error(f"❌ Erreur fatale: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
